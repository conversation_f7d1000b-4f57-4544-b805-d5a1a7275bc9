// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get appTitle => 'Wargani';

  @override
  String get developedBy => 'Developed by AMSSoftX | https://amssoftx.com';

  @override
  String get profile => 'प्रोफाइल';

  @override
  String get mandalName => 'मंडल का नाम';

  @override
  String get address => 'पता';

  @override
  String get logo => 'Logo';

  @override
  String get currentYear => 'वर्तमान वर्ष';

  @override
  String get save => 'सेव करें';

  @override
  String get dashboard => 'डैशबोर्ड';

  @override
  String get totalWargani => 'Total Wargani';

  @override
  String get totalDonations => 'Total Donations';

  @override
  String get totalExpenses => 'Total Expenses';

  @override
  String get warganiReceipt => 'वरगणी रसीद';

  @override
  String get receiptNo => 'रसीद नंबर';

  @override
  String get date => 'दिनांक';

  @override
  String get name => 'नाम';

  @override
  String get amount => 'राशि';

  @override
  String get amountInWords => 'राशि शब्दों में';

  @override
  String get generatePdf => 'Generate PDF';

  @override
  String get share => 'साझा करें';

  @override
  String get download => 'डाउनलोड';

  @override
  String get thankYouNote => 'Thank you for your contribution.';

  @override
  String get expenses => 'खर्च';

  @override
  String get title => 'शीर्षक';

  @override
  String get description => 'विवरण';

  @override
  String get addExpense => 'Add Expense';

  @override
  String get donations => 'दान';

  @override
  String get donorName => 'दाता का नाम';

  @override
  String get reason => 'कारण';

  @override
  String get addDonation => 'Add Donation';

  @override
  String get prefix => 'उपसर्ग';

  @override
  String get warganiSummary => 'वरगणी सारांश';

  @override
  String get donationSummary => 'दान सारांश';

  @override
  String get expensesSummary => 'खर्च सारांश';

  @override
  String get totalPeople => 'कुल लोग';

  @override
  String get totalAmount => 'कुल राशि';

  @override
  String get login => 'लॉगिन';

  @override
  String get email => 'ईमेल';

  @override
  String get password => 'पासवर्ड';

  @override
  String get pleaseEnterEmail => 'कृपया ईमेल दर्ज करें';

  @override
  String get pleaseEnterPassword => 'कृपया पासवर्ड दर्ज करें';

  @override
  String get noUserFound => 'No user found for that email.';

  @override
  String get wrongPassword => 'गलत पासवर्ड';

  @override
  String loginFailed(Object errorMessage) {
    return 'Login failed: $errorMessage';
  }

  @override
  String get appName => 'वरगणी ऐप';

  @override
  String get dontHaveAccount => 'Don\'t have an account? Sign up';

  @override
  String get forgotPassword => 'पासवर्ड भूल गए?';

  @override
  String get forgotPasswordMessage =>
      'Forgot password functionality is not yet implemented.';

  @override
  String get signUp => 'Sign Up';

  @override
  String get signUpSuccess => 'सफलतापूर्वक साइन अप हो गए';

  @override
  String get weakPassword => 'The password provided is too weak.';

  @override
  String get emailAlreadyInUse => 'ईमेल पहले से उपयोग में है';

  @override
  String signUpFailed(Object errorMessage) {
    return 'Sign up failed: $errorMessage';
  }

  @override
  String get createAccount => 'Create New Account';

  @override
  String get pleaseEnterName => 'कृपया नाम दर्ज करें';

  @override
  String get alreadyHaveAccount => 'Already have an account? Login';

  @override
  String get cancel => 'रद्द करें';

  @override
  String get sendResetLink => 'Send Reset Link';

  @override
  String get passwordResetEmailSent =>
      'Password reset email sent. Check your inbox.';

  @override
  String passwordResetFailed(Object errorMessage) {
    return 'Password reset failed: $errorMessage';
  }

  @override
  String get userNotFound => 'उपयोगकर्ता नहीं मिला';

  @override
  String get passwordResetSuccess => 'Password reset successfully';

  @override
  String get newPassword => 'New Password';

  @override
  String get pleaseEnterNewPassword => 'Please enter new password';

  @override
  String get resetPassword => 'पासवर्ड रीसेट करें';

  @override
  String get findUser => 'Find User';

  @override
  String get pleaseEnterMandalName => 'Please enter Mandal Name';

  @override
  String get pleaseEnterAddress => 'Please enter Address';

  @override
  String get pleaseEnterCurrentYear => 'Please enter Current Year';

  @override
  String get profileSaved => 'प्रोफाइल सेव हो गया';

  @override
  String get userName => 'User Name';

  @override
  String get userEmail => 'User Email';

  @override
  String get pleaseEnterReceiptNo => 'Please enter a receipt number';

  @override
  String get pleaseEnterPrefix => 'Please enter a prefix';

  @override
  String get pleaseEnterAmount => 'कृपया राशि दर्ज करें';

  @override
  String get pleaseEnterValidAmount => 'Please enter a valid amount';

  @override
  String get pleaseEnterValidNumber => 'Please enter a valid number';

  @override
  String get pleaseEnterRegistrationNo => 'Please enter a registration number';

  @override
  String get pleaseEnterAmountInWords => 'Please enter amount in words';

  @override
  String get pdfGenerated => 'PDF Generated';

  @override
  String get pdfGeneratedSuccessfully => 'PDF has been generated successfully.';

  @override
  String get ok => 'OK';

  @override
  String get saveReceipt => 'Save Receipt';

  @override
  String get clearForm => 'Clear Form';

  @override
  String get preview => 'पूर्वावलोकन';

  @override
  String get mobileNo => 'Mobile No.';

  @override
  String get generatedBy => 'Generated By';

  @override
  String get pleaseEnterDonorName => 'Please enter a donor name';

  @override
  String get noDonationsYet => 'No donations yet.';

  @override
  String get pleaseEnterTitle => 'Please enter a title';

  @override
  String get noExpensesYet => 'No expenses yet.';

  @override
  String get downloadAllExpenses => 'Download All Expenses';

  @override
  String get shareAllExpenses => 'Share All Expenses';

  @override
  String get netBalance => 'शुद्ध बैलेंस';

  @override
  String get selectLanguage => 'भाषा चुनें';

  @override
  String get shreeGaneshPrasanna => '|| श्री गणेश प्रसन्न ||';

  @override
  String get registrationNo => 'Registration No.';

  @override
  String ganeshotsavYear(Object year) {
    return 'Ganeshotsav $year';
  }

  @override
  String get from => 'From';

  @override
  String get cashReceived => 'Cash Received...!';

  @override
  String get thankYou => 'Thank You ...!';

  @override
  String get signature => 'Signature';

  @override
  String get logout => 'लॉगआउट';

  @override
  String get secretQuestion => 'गुप्त प्रश्न';

  @override
  String get pleaseEnterSecretQuestion => 'कृपया गुप्त प्रश्न दर्ज करें';

  @override
  String get secretAnswer => 'गुप्त उत्तर';

  @override
  String get pleaseEnterSecretAnswer => 'कृपया गुप्त उत्तर दर्ज करें';

  @override
  String get paymentMethod => 'भुगतान विधि';

  @override
  String get cash => 'नकद';

  @override
  String get upi => 'यूपीआई';

  @override
  String get card => 'कार्ड';

  @override
  String get qrCode => 'क्यूआर कोड';

  @override
  String get selectPaymentMethod => 'भुगतान विधि चुनें';

  @override
  String get deleteReceipt => 'रसीद हटाएं';

  @override
  String deleteReceiptConfirmation(Object receiptNo) {
    return 'क्या आप वाकई रसीद #$receiptNo को हटाना चाहते हैं?\n\nसुरक्षा के लिए रसीद को बिन फोल्डर में स्थानांतरित कर दिया जाएगा और बाद में इसे पुनर्प्राप्त किया जा सकता है।';
  }

  @override
  String get delete => 'हटाएं';

  @override
  String receiptMovedToBin(Object receiptNo) {
    return 'रसीद #$receiptNo सफलतापूर्वक बिन फोल्डर में स्थानांतरित!';
  }

  @override
  String failedToDeleteReceipt(Object error) {
    return 'रसीद हटाने में विफल: $error';
  }

  @override
  String get upiQrCode => 'यूपीआई क्यूआर कोड';

  @override
  String get close => 'बंद करें';

  @override
  String get noQrCodeFound =>
      'प्रोफाइल में यूपीआई क्यूआर कोड नहीं मिला। कृपया पहले क्यूआर कोड अपलोड करें।';
}
