import 'package:hive/hive.dart';
import 'dart:typed_data'; // Import Uint8List

part 'profile_model.g.dart';

@HiveType(typeId: 0)
class Profile extends HiveObject {
  @HiveField(0)
  String mandalName;

  @HiveField(1)
  String address;

  @HiveField(2)
  Uint8List? logoBytes; // Change from logoPath to logoBytes

  @HiveField(3)
  String currentYear;

  @HiveField(4)
  String? mandalRegistrationNo;

  @HiveField(5)
  String? festivalName;

  @HiveField(6)
  Uint8List? leftLogoBytes;

  @HiveField(7)
  Uint8List? rightLogoBytes;

  @HiveField(8)
  String? customHeaderText;

  @HiveField(9)
  String? leftHeaderText; // Left header text for receipts

  @HiveField(10)
  String? middleHeaderText; // Middle header text for receipts

  @HiveField(11)
  String? rightHeaderText; // Right header text for receipts

  @HiveField(12)
  Uint8List? upiQrCodeBytes; // UPI QR code scanner image

  @HiveField(13)
  String? customFestivalName; // Custom festival name instead of "Ganeshotsav"

  Profile({
    required this.mandalName,
    required this.address,
    this.logoBytes, // Update constructor parameter
    required this.currentYear,
    this.mandalRegistrationNo,
    this.festivalName,
    this.leftLogoBytes,
    this.rightLogoBytes,
    this.customHeaderText,
    this.leftHeaderText,
    this.middleHeaderText,
    this.rightHeaderText,
    this.upiQrCodeBytes,
    this.customFestivalName,
  });
}
