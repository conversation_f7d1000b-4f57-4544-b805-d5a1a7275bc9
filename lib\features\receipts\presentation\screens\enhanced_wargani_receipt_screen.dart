import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/core/utils/validators.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/shared/widgets/custom_button.dart';
import 'package:wargani/shared/widgets/custom_card.dart';
import 'package:wargani/shared/widgets/custom_text_field.dart';
import 'package:wargani/shared/widgets/loading_overlay.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

/// Enhanced Wargani Receipt Screen with professional UI
class EnhancedWarganiReceiptScreen extends StatefulWidget {
  const EnhancedWarganiReceiptScreen({super.key});

  @override
  State<EnhancedWarganiReceiptScreen> createState() => _EnhancedWarganiReceiptScreenState();
}

class _EnhancedWarganiReceiptScreenState extends State<EnhancedWarganiReceiptScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();

  // Controllers
  final _receiptNoController = TextEditingController();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _prefixController = TextEditingController();
  final _mobileNoController = TextEditingController();
  final _registrationNoController = TextEditingController();
  final _amountInWordsController = TextEditingController();

  // Focus Nodes
  final _receiptNoFocus = FocusNode();
  final _nameFocus = FocusNode();
  final _amountFocus = FocusNode();
  final _prefixFocus = FocusNode();
  final _mobileFocus = FocusNode();
  final _registrationFocus = FocusNode();
  final _amountWordsFocus = FocusNode();

  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  int _currentPageIndex = 0;
  String _selectedPrefix = 'Mr.';
  String _selectedPaymentMethod = 'Cash'; // New payment method field

  late AnimationController _animationController;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _setupControllers();
    _loadNextReceiptNumber();
    _loadProfileData();
  }

  void _setupControllers() {
    _animationController = AnimationController(
      duration: AppConstants.mediumAnimation,
      vsync: this,
    );
    _tabController = TabController(length: 2, vsync: this);
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    _pageController.dispose();

    // Dispose controllers
    _receiptNoController.dispose();
    _nameController.dispose();
    _amountController.dispose();
    _prefixController.dispose();
    _mobileNoController.dispose();
    _registrationNoController.dispose();
    _amountInWordsController.dispose();

    // Dispose focus nodes
    _receiptNoFocus.dispose();
    _nameFocus.dispose();
    _amountFocus.dispose();
    _prefixFocus.dispose();
    _mobileFocus.dispose();
    _registrationFocus.dispose();
    _amountWordsFocus.dispose();

    super.dispose();
  }

  void _loadNextReceiptNumber() {
    final warganiBox = HiveHelper.getWarganiBox();
    final nextReceiptNo = warganiBox.length + 1;
    _receiptNoController.text = nextReceiptNo.toString();
  }

  void _loadProfileData() {
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isNotEmpty) {
      final profile = profileBox.getAt(0);
      if (profile?.mandalRegistrationNo != null) {
        _registrationNoController.text = profile!.mandalRegistrationNo!;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return LoadingOverlay(
      isLoading: _isLoading,
      message: 'Processing receipt...',
      child: Scaffold(
        backgroundColor: context.colorScheme.background,
        appBar: _buildAppBar(localizations),
        body: Column(
          children: [
            _buildTabBar(localizations),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildReceiptForm(localizations),
                  _buildReceiptsList(localizations),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(AppLocalizations localizations) {
    return AppBar(
      title: Text(localizations.warganiReceipt),
      backgroundColor: context.colorScheme.surface,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh_rounded),
          onPressed: _loadNextReceiptNumber,
          tooltip: 'Refresh Receipt Number',
        ),
      ],
    );
  }

  Widget _buildTabBar(AppLocalizations localizations) {
    return Container(
      color: context.colorScheme.surface,
      child: TabBar(
        controller: _tabController,
        tabs: [
          Tab(
            icon: const Icon(Icons.add_rounded),
            text: 'New Receipt',
          ),
          Tab(
            icon: const Icon(Icons.list_rounded),
            text: 'All Receipts',
          ),
        ],
        labelColor: context.colorScheme.primary,
        unselectedLabelColor: context.colorScheme.onSurface.withValues(alpha: 0.6),
        indicatorColor: context.colorScheme.primary,
      ),
    );
  }

  Widget _buildPrefixDropdown(AppLocalizations localizations) {
    final isMarathi = localizations.localeName == 'mr';
    final isHindi = localizations.localeName == 'hi';

    List<String> prefixOptions;
    String defaultPrefix;

    if (isMarathi) {
      prefixOptions = ['श्री', 'श्रीमती', 'डॉ.', 'प्रा.', 'कु.'];
      defaultPrefix = 'श्री';
    } else if (isHindi) {
      prefixOptions = ['श्री', 'श्रीमती', 'डॉ.', 'प्रो.', 'कु.'];
      defaultPrefix = 'श्री';
    } else {
      prefixOptions = ['Mr.', 'Mrs.', 'Dr.', 'Prof.', 'Miss'];
      defaultPrefix = 'Mr.';
    }

    // Ensure selected prefix is valid for current language
    if (!prefixOptions.contains(_selectedPrefix)) {
      _selectedPrefix = defaultPrefix;
      _prefixController.text = defaultPrefix;
    }

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: context.colorScheme.outline),
      ),
      child: DropdownButtonFormField<String>(
        value: _selectedPrefix,
        decoration: InputDecoration(
          hintText: 'Select prefix',
          prefixIcon: const Icon(Icons.person_outline_rounded),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          isDense: true,
        ),
        isExpanded: true,
        items: prefixOptions.map((String prefix) {
          return DropdownMenuItem<String>(
            value: prefix,
            child: Text(prefix),
          );
        }).toList(),
        onChanged: (String? newValue) {
          if (newValue != null) {
            setState(() {
              _selectedPrefix = newValue;
              _prefixController.text = newValue;
            });
          }
        },
        validator: (value) => Validators.validateRequired(value, 'Prefix'),
      ),
    );
  }

  Widget _buildPaymentMethodDropdown(AppLocalizations localizations) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: context.colorScheme.outline),
      ),
      child: DropdownButtonFormField<String>(
        value: _selectedPaymentMethod,
        decoration: InputDecoration(
          hintText: localizations.selectPaymentMethod,
          prefixIcon: const Icon(Icons.payment_rounded),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        items: [
          DropdownMenuItem(value: 'Cash', child: Text(localizations.cash)),
          DropdownMenuItem(value: 'UPI', child: Text(localizations.upi)),
          DropdownMenuItem(value: 'Card', child: Text(localizations.card)),
          DropdownMenuItem(value: 'QR Code', child: Text(localizations.qrCode)),
        ],
        onChanged: (String? newValue) {
          if (newValue != null) {
            setState(() {
              _selectedPaymentMethod = newValue;
            });
            // Show QR code popup if QR Code is selected
            if (newValue == 'QR Code') {
              _showQrCodePopup();
            }
          }
        },
        validator: (value) => Validators.validateRequired(value, 'Payment method'),
      ),
    );
  }

  void _showQrCodePopup() {
    final localizations = AppLocalizations.of(context)!;
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      context.showErrorSnackBar('Please set up profile first');
      return;
    }

    final profile = profileBox.getAt(0)!;
    if (profile.bigImageBytes == null) {
      context.showErrorSnackBar(localizations.noQrCodeFound);
      return;
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                localizations.upiQrCode,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Image.memory(
                profile.bigImageBytes!,
                fit: BoxFit.contain,
                height: 300,
                width: 300,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: Text(localizations.close),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReceiptForm(AppLocalizations localizations) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFormHeader(localizations),
            const SizedBox(height: AppConstants.largePadding),
            _buildBasicInfoSection(localizations),
            const SizedBox(height: AppConstants.largePadding),
            _buildContactInfoSection(localizations),
            const SizedBox(height: AppConstants.largePadding),
            _buildAdditionalInfoSection(localizations),
            const SizedBox(height: AppConstants.largePadding),
            _buildActionButtons(localizations),
            const SizedBox(height: AppConstants.largePadding),
          ],
        ),
      ),
    ).animate().fadeIn().slideY(begin: 0.3, end: 0);
  }

  Widget _buildFormHeader(AppLocalizations localizations) {
    return CustomCard(
      backgroundColor: context.colorScheme.primaryContainer,
      child: Column(
        children: [
          Icon(
            Icons.receipt_long_rounded,
            size: 48,
            color: context.colorScheme.primary,
          ),
          const SizedBox(height: 12),
          Text(
            'Create New Receipt',
            style: context.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: context.colorScheme.onPrimaryContainer,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Fill in the details to generate a professional receipt',
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection(AppLocalizations localizations) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: CustomTextField(
                  label: localizations.receiptNo,
                  controller: _receiptNoController,
                  focusNode: _receiptNoFocus,
                  keyboardType: TextInputType.number,
                  prefixIcon: Icons.numbers_rounded,
                  validator: Validators.validateReceiptNumber,
                  textInputAction: TextInputAction.next,
                  onSubmitted: (_) => _prefixFocus.requestFocus(),
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                flex: 2,
                child: _buildPrefixDropdown(localizations),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomTextField(
            label: localizations.name,
            hint: 'Enter full name',
            controller: _nameController,
            focusNode: _nameFocus,
            prefixIcon: Icons.person_rounded,
            validator: Validators.validateName,
            textInputAction: TextInputAction.next,
            onSubmitted: (_) => _amountFocus.requestFocus(),
            enableTranslation: true,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomTextField(
            label: localizations.amount,
            hint: 'Enter amount in ₹',
            controller: _amountController,
            focusNode: _amountFocus,
            keyboardType: TextInputType.number,
            prefixIcon: Icons.currency_rupee_rounded,
            validator: Validators.validateAmount,
            textInputAction: TextInputAction.next,
            onSubmitted: (_) => _amountWordsFocus.requestFocus(),
            onChanged: _onAmountChanged,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomTextField(
            label: localizations.amountInWords,
            hint: 'Amount in words',
            controller: _amountInWordsController,
            focusNode: _amountWordsFocus,
            prefixIcon: Icons.text_fields_rounded,
            validator: (value) => Validators.validateRequired(value, 'Amount in words'),
            textInputAction: TextInputAction.next,
            onSubmitted: (_) => _mobileFocus.requestFocus(),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildPaymentMethodDropdown(localizations),
        ],
      ),
    );
  }

  Widget _buildContactInfoSection(AppLocalizations localizations) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Contact Information',
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomTextField(
            label: localizations.mobileNo,
            hint: 'Enter 10-digit mobile number',
            controller: _mobileNoController,
            focusNode: _mobileFocus,
            keyboardType: TextInputType.phone,
            prefixIcon: Icons.phone_rounded,
            validator: Validators.validateMobileNumber,
            textInputAction: TextInputAction.next,
            onSubmitted: (_) => _registrationFocus.requestFocus(),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          CustomTextField(
            label: localizations.registrationNo,
            hint: 'Mandal registration number',
            controller: _registrationNoController,
            focusNode: _registrationFocus,
            prefixIcon: Icons.badge_rounded,
            textInputAction: TextInputAction.done,
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoSection(AppLocalizations localizations) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Additional Information',
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Row(
            children: [
              Icon(
                Icons.calendar_today_rounded,
                color: context.colorScheme.primary,
              ),
              const SizedBox(width: 12),
              Text(
                '${localizations.date}: ${_selectedDate.toDisplayDate}',
                style: context.textTheme.bodyLarge,
              ),
              const Spacer(),
              CustomButton(
                text: 'Change Date',
                variant: ButtonVariant.secondary,
                size: ButtonSize.small,
                onPressed: _selectDate,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(AppLocalizations localizations) {
    return Column(
      children: [
        CustomButton(
          text: localizations.saveReceipt,
          onPressed: _saveReceipt,
          isLoading: _isLoading,
          isFullWidth: true,
          size: ButtonSize.large,
          icon: Icons.save_rounded,
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: localizations.clearForm,
                variant: ButtonVariant.secondary,
                onPressed: _clearForm,
                icon: Icons.clear_rounded,
              ),
            ),

          ],
        ),
      ],
    );
  }

  Widget _buildReceiptsList(AppLocalizations localizations) {
    return ValueListenableBuilder(
      valueListenable: HiveHelper.getWarganiBox().listenable(),
      builder: (context, Box<Wargani> box, _) {
        final receipts = box.values.toList().cast<Wargani>();

        if (receipts.isEmpty) {
          return _buildEmptyState(localizations);
        }

        return Column(
          children: [
            // Header with donors report button
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      '${localizations.allReceipts} (${receipts.length})',
                      style: context.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      CustomButton(
                        text: 'Download Report',
                        variant: ButtonVariant.secondary,
                        size: ButtonSize.small,
                        icon: Icons.download_rounded,
                        onPressed: () => _downloadDonorsReport(),
                      ),
                      const SizedBox(width: 8),
                      CustomButton(
                        text: 'Share Report',
                        variant: ButtonVariant.secondary,
                        size: ButtonSize.small,
                        icon: Icons.share_rounded,
                        onPressed: () => _shareDonorsReport(),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Receipts list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                itemCount: receipts.length,
                itemBuilder: (context, index) {
                  final receipt = receipts[index];
                  return _buildReceiptCard(receipt, localizations, index);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_rounded,
            size: 64,
            color: context.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No receipts yet',
            style: context.textTheme.headlineSmall?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first receipt to get started',
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReceiptCard(Wargani receipt, AppLocalizations localizations, int index) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '#${receipt.receiptNo}',
                  style: context.textTheme.labelLarge?.copyWith(
                    color: context.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                receipt.date.toDisplayDate,
                style: context.textTheme.bodySmall?.copyWith(
                  color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '${receipt.prefix} ${receipt.name}',
            style: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            receipt.amount.toCurrencyCompact,
            style: context.textTheme.headlineSmall?.copyWith(
              color: context.colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (receipt.mobileNo != null && receipt.mobileNo!.isNotEmpty) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.phone_rounded,
                  size: 16,
                  color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                const SizedBox(width: 4),
                Text(
                  receipt.mobileNo!,
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ],
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: localizations.download,
                  variant: ButtonVariant.secondary,
                  size: ButtonSize.small,
                  icon: Icons.download_rounded,
                  onPressed: () => _downloadPdf(receipt),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: CustomButton(
                  text: 'WhatsApp Share',
                  size: ButtonSize.small,
                  icon: Icons.share_rounded,
                  onPressed: () => _sharePdf(receipt),
                ),
              ),
              const SizedBox(width: 8),
              CustomButton(
                text: '',
                variant: ButtonVariant.secondary,
                size: ButtonSize.small,
                icon: Icons.delete_rounded,
                onPressed: () => _deleteReceipt(receipt),
              ),
            ],
          ),
        ],
      ),
    ).animate(delay: (index * 100).ms).fadeIn().slideX(begin: 0.3, end: 0);
  }

  // Helper Methods
  void _onAmountChanged(String value) {
    if (value.isNotEmpty) {
      final amount = double.tryParse(value);
      if (amount != null) {
        // Auto-generate amount in words with language support
        final localizations = AppLocalizations.of(context)!;
        _amountInWordsController.text = _numberToWords(amount, localizations);
      }
    }
  }

  String _numberToWords(double amount, AppLocalizations localizations) {
    final intAmount = amount.toInt();
    final isMarathi = localizations.localeName == 'mr';
    final isHindi = localizations.localeName == 'hi';

    if (isMarathi) {
      return _numberToMarathiWords(intAmount);
    } else if (isHindi) {
      return _numberToHindiWords(intAmount);
    } else {
      return _numberToEnglishWords(intAmount);
    }
  }

  String _numberToEnglishWords(int amount) {
    if (amount == 0) return 'Zero Rupees Only';

    List<String> ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
    List<String> teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
    List<String> tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];

    String result = '';

    if (amount >= 10000000) { // Crores
      int crores = amount ~/ 10000000;
      result += '${_convertHundreds(crores, ones, teens, tens)} Crore ';
      amount %= 10000000;
    }

    if (amount >= 100000) { // Lakhs
      int lakhs = amount ~/ 100000;
      result += '${_convertHundreds(lakhs, ones, teens, tens)} Lakh ';
      amount %= 100000;
    }

    if (amount >= 1000) { // Thousands
      int thousands = amount ~/ 1000;
      result += '${_convertHundreds(thousands, ones, teens, tens)} Thousand ';
      amount %= 1000;
    }

    if (amount > 0) {
      result += _convertHundreds(amount, ones, teens, tens);
    }

    return '${result.trim()} Rupees Only';
  }

  String _convertHundreds(int num, List<String> ones, List<String> teens, List<String> tens) {
    String result = '';

    if (num >= 100) {
      result += '${ones[num ~/ 100]} Hundred ';
      num %= 100;
    }

    if (num >= 20) {
      result += '${tens[num ~/ 10]} ';
      num %= 10;
    } else if (num >= 10) {
      result += '${teens[num - 10]} ';
      return result;
    }

    if (num > 0) {
      result += '${ones[num]} ';
    }

    return result;
  }

  String _numberToMarathiWords(int amount) {
    if (amount == 0) return 'शून्य रुपये फक्त';

    List<String> ones = ['', 'एक', 'दोन', 'तीन', 'चार', 'पाच', 'सहा', 'सात', 'आठ', 'नऊ'];
    List<String> teens = ['दहा', 'अकरा', 'बारा', 'तेरा', 'चौदा', 'पंधरा', 'सोळा', 'सतरा', 'अठरा', 'एकोणीस'];
    List<String> tens = ['', '', 'वीस', 'तीस', 'चाळीस', 'पन्नास', 'साठ', 'सत्तर', 'ऐंशी', 'नव्वद'];

    String result = '';

    if (amount >= 10000000) { // कोटी
      int crores = amount ~/ 10000000;
      result += '${_convertMarathiHundreds(crores, ones, teens, tens)} कोटी ';
      amount %= 10000000;
    }

    if (amount >= 100000) { // लाख
      int lakhs = amount ~/ 100000;
      result += '${_convertMarathiHundreds(lakhs, ones, teens, tens)} लाख ';
      amount %= 100000;
    }

    if (amount >= 1000) { // हजार
      int thousands = amount ~/ 1000;
      result += '${_convertMarathiHundreds(thousands, ones, teens, tens)} हजार ';
      amount %= 1000;
    }

    if (amount > 0) {
      result += _convertMarathiHundreds(amount, ones, teens, tens);
    }

    return '${result.trim()} रुपये फक्त';
  }

  String _convertMarathiHundreds(int num, List<String> ones, List<String> teens, List<String> tens) {
    String result = '';

    if (num >= 100) {
      result += '${ones[num ~/ 100]} शे ';
      num %= 100;
    }

    if (num >= 20) {
      result += '${tens[num ~/ 10]} ';
      num %= 10;
    } else if (num >= 10) {
      result += '${teens[num - 10]} ';
      return result;
    }

    if (num > 0) {
      result += '${ones[num]} ';
    }

    return result;
  }

  String _numberToHindiWords(int amount) {
    if (amount == 0) return 'शून्य रुपये मात्र';

    List<String> ones = ['', 'एक', 'दो', 'तीन', 'चार', 'पांच', 'छह', 'सात', 'आठ', 'नौ'];
    List<String> teens = ['दस', 'ग्यारह', 'बारह', 'तेरह', 'चौदह', 'पंद्रह', 'सोलह', 'सत्रह', 'अठारह', 'उन्नीस'];
    List<String> tens = ['', '', 'बीस', 'तीस', 'चालीस', 'पचास', 'साठ', 'सत्तर', 'अस्सी', 'नब्बे'];

    String result = '';

    if (amount >= 10000000) { // करोड़
      int crores = amount ~/ 10000000;
      result += '${_convertHindiHundreds(crores, ones, teens, tens)} करोड़ ';
      amount %= 10000000;
    }

    if (amount >= 100000) { // लाख
      int lakhs = amount ~/ 100000;
      result += '${_convertHindiHundreds(lakhs, ones, teens, tens)} लाख ';
      amount %= 100000;
    }

    if (amount >= 1000) { // हजार
      int thousands = amount ~/ 1000;
      result += '${_convertHindiHundreds(thousands, ones, teens, tens)} हजार ';
      amount %= 1000;
    }

    if (amount > 0) {
      result += _convertHindiHundreds(amount, ones, teens, tens);
    }

    return '${result.trim()} रुपये मात्र';
  }

  String _convertHindiHundreds(int num, List<String> ones, List<String> teens, List<String> tens) {
    String result = '';

    if (num >= 100) {
      result += '${ones[num ~/ 100]} सौ ';
      num %= 100;
    }

    if (num >= 20) {
      result += '${tens[num ~/ 10]} ';
      num %= 10;
    } else if (num >= 10) {
      result += '${teens[num - 10]} ';
      return result;
    }

    if (num > 0) {
      result += '${ones[num]} ';
    }

    return result;
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _saveReceipt() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final wargani = Wargani(
          receiptNo: int.parse(_receiptNoController.text),
          name: _nameController.text.trim(),
          amount: double.parse(_amountController.text),
          date: _selectedDate,
          prefix: _selectedPrefix,
          mobileNo: _mobileNoController.text.trim().isNotEmpty
              ? _mobileNoController.text.trim()
              : null,
          registrationNo: _registrationNoController.text.trim(),
          amountInWords: _amountInWordsController.text.trim(),
          paymentMethod: _selectedPaymentMethod, // Add payment method
        );

        await HiveHelper.getWarganiBox().add(wargani);
        _clearForm();
        _loadNextReceiptNumber();

        if (mounted) {
          context.showSuccessSnackBar('Receipt saved successfully!');
          _showSaveSuccessDialog(wargani);
        }
      } catch (e) {
        if (mounted) {
          context.showErrorSnackBar('Failed to save receipt: ${e.toString()}');
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _showSaveSuccessDialog(Wargani wargani) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            const SizedBox(width: 8),
            const Text('Receipt Saved!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Your Wargani receipt has been saved successfully.'),
            const SizedBox(height: 16),
            const Text('Choose an action:'),
          ],
        ),
        actions: [
          Row(
            children: [
              Expanded(
                child: TextButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _previewPdf(wargani);
                  },
                  icon: const Icon(Icons.preview),
                  label: const Text('Preview'),
                ),
              ),
              Expanded(
                child: TextButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _downloadPdf(wargani);
                  },
                  icon: const Icon(Icons.download),
                  label: const Text('Download'),
                ),
              ),
              Expanded(
                child: TextButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _sharePdf(wargani);
                  },
                  icon: const Icon(Icons.share),
                  label: const Text('Share'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _tabController.animateTo(1); // Go to receipts list
                  },
                  child: const Text('View Saved Receipts'),
                ),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _clearForm() {
    _nameController.clear();
    _amountController.clear();
    _prefixController.clear();
    _mobileNoController.clear();
    _registrationNoController.clear();
    _amountInWordsController.clear();
    _selectedDate = DateTime.now();
    setState(() {});
  }
  Future<void> _deleteReceipt(Wargani receipt) async {
    // Show confirmation dialog
    final localizations = AppLocalizations.of(context)!;

    // Debug: Show that delete button was clicked
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Delete button clicked for receipt ${receipt.receiptNo}')),
    );

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.deleteReceipt),
        content: Text(
          'Are you sure you want to move receipt #${receipt.receiptNo} to bin folder? You can restore it later from the bin.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(localizations.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text('Move to Bin'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Move receipt to bin folder (deleted_wargani box)
        final deletedBox = HiveHelper.getDeletedWarganiBox();
        await deletedBox.add(receipt);

        // Remove from main wargani box
        final warganiBox = HiveHelper.getWarganiBox();
        final receiptKey = warganiBox.keys.firstWhere(
          (key) => warganiBox.get(key)?.receiptNo == receipt.receiptNo,
          orElse: () => null,
        );

        if (receiptKey != null) {
          await warganiBox.delete(receiptKey);
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ Receipt #${receipt.receiptNo} moved to bin folder successfully! You can restore it from Dashboard → Bin Folder.'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          context.showErrorSnackBar(localizations.failedToDeleteReceipt(e.toString()));
        }
      }
    }
  }

  Future<void> _downloadDonorsReport() async {
    final localizations = AppLocalizations.of(context)!;
    try {
      setState(() {
        _isLoading = true;
      });

      final pdfPath = await PdfGenerator.generateDonorsReport(context);

      if (pdfPath != null && mounted) {
        // For web, the PDF is already handled by Printing.layoutPdf
        // For mobile, we need to save to downloads folder
        if (kIsWeb) {
          if (mounted) {
            context.showSuccessSnackBar('Donors report downloaded successfully!');
          }
        } else {
          // Save to downloads folder on mobile
          try {
            final downloadsDir = Directory('/storage/emulated/0/Download');
            if (await downloadsDir.exists()) {
              final fileName = 'Donors_Report_${DateTime.now().millisecondsSinceEpoch}.pdf';
              final downloadFile = File('${downloadsDir.path}/$fileName');
              await downloadFile.writeAsBytes(await File(pdfPath).readAsBytes());
              if (mounted) {
                context.showSuccessSnackBar('Donors report saved to Downloads folder: $fileName');
              }
            } else {
              // Fallback to sharing if downloads folder not accessible
              await Share.shareXFiles([XFile(pdfPath)]);
              if (mounted) {
                context.showSuccessSnackBar('Donors report shared successfully!');
              }
            }
          } catch (e) {
            // Fallback to sharing if saving fails
            await Share.shareXFiles([XFile(pdfPath)]);
            if (mounted) {
              context.showSuccessSnackBar('Donors report shared successfully!');
            }
          }
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar(localizations.failedToGenerateReport(e.toString()));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _shareDonorsReport() async {
    final localizations = AppLocalizations.of(context)!;
    try {
      setState(() {
        _isLoading = true;
      });

      final pdfPath = await PdfGenerator.generateDonorsReport(context);

      if (pdfPath != null && mounted) {
        // Share the donors report
        final profile = HiveHelper.getProfileBox().getAt(0);
        final mandalName = profile?.mandalName ?? 'Mandal';

        await Share.shareXFiles(
          [XFile(pdfPath)],
          text: 'Donors Report from $mandalName\n\nGenerated on ${DateFormat('dd/MM/yyyy').format(DateTime.now())}',
          subject: 'Donors Report - $mandalName',
        );

        if (mounted) {
          context.showSuccessSnackBar('Donors report shared successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar(localizations.failedToGenerateReport(e.toString()));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }





  Future<void> _downloadPdf(Wargani wargani) async {
    try {
      // Show language selection dialog
      final selectedLanguage = await _showLanguageSelectionDialog();

      // If user cancelled the dialog, return gracefully
      if (selectedLanguage == null) {
        return; // User cancelled, no need to show any message
      }

      if (!mounted) return;

      setState(() {
        _isLoading = true;
      });

      final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
        context,
        wargani,
        HiveHelper.getUsersBox().getAt(0)?.name,
        forceLanguage: selectedLanguage,
      );

      if (pdfPath != null && mounted) {
        // For web, the PDF is already handled by Printing.layoutPdf
        // For mobile, we need to save to downloads folder
        if (kIsWeb) {
          if (mounted) {
            context.showSuccessSnackBar('PDF downloaded successfully!');
          }
        } else {
          // Save to downloads folder on mobile
          try {
            final downloadsDir = Directory('/storage/emulated/0/Download');
            if (await downloadsDir.exists()) {
              final sanitizedName = wargani.name.replaceAll(RegExp(r'[^\w\s-]'), '').replaceAll(' ', '_');
              final fileName = 'Receipt_${wargani.receiptNo}_$sanitizedName.pdf';
              final downloadFile = File('${downloadsDir.path}/$fileName');
              await downloadFile.writeAsBytes(await File(pdfPath).readAsBytes());
              if (mounted) {
                context.showSuccessSnackBar('PDF saved to Downloads folder: $fileName');
              }
            } else {
              // Fallback to sharing if downloads folder not accessible
              await Share.shareXFiles([XFile(pdfPath)]);
              if (mounted) {
                context.showSuccessSnackBar('PDF shared successfully!');
              }
            }
          } catch (e) {
            // Fallback to sharing if saving fails
            await Share.shareXFiles([XFile(pdfPath)]);
            if (mounted) {
              context.showSuccessSnackBar('PDF shared successfully!');
            }
          }
        }
      }
    } catch (e) {
      print('Error in _downloadPdf: $e');
      if (mounted) {
        context.showErrorSnackBar('Failed to generate PDF: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _sharePdf(Wargani wargani) async {
    try {
      // Show language selection dialog
      final selectedLanguage = await _showLanguageSelectionDialog();

      // If user cancelled the dialog, return gracefully
      if (selectedLanguage == null) {
        return; // User cancelled, no need to show any message
      }

      if (!mounted) return;

      setState(() {
        _isLoading = true;
      });

      final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
        context,
        wargani,
        HiveHelper.getUsersBox().getAt(0)?.name,
        forceLanguage: selectedLanguage,
      );

      if (pdfPath != null && mounted) {
        if (wargani.mobileNo != null && wargani.mobileNo!.isNotEmpty) {
          // Try to share directly to WhatsApp first
          try {
            // Create WhatsApp message
            final message = 'धन्यवाद! आपल्या ₹${wargani.amount} च्या वरगणीसाठी. 🙏\n\nThank you for your generous contribution of ₹${wargani.amount} to our Ganesh Mandal! 🕉️\n\nReceipt attached.';

            // Share with specific WhatsApp intent
            await Share.shareXFiles(
              [XFile(pdfPath)],
              text: message,
              subject: 'Wargani Receipt - ${wargani.name}',
            );

            if (mounted) {
              context.showSuccessSnackBar('Receipt shared to WhatsApp successfully!');
            }
          } catch (e) {
            // Fallback to regular share
            await Share.shareXFiles([XFile(pdfPath)]);
            if (mounted) {
              context.showSuccessSnackBar('Receipt shared successfully!');
            }
          }
        } else {
          // Regular share without mobile number
          await Share.shareXFiles(
            [XFile(pdfPath)],
            text: 'Wargani Receipt - ${wargani.name}',
            subject: 'Receipt from ${HiveHelper.getProfileBox().getAt(0)?.mandalName ?? "Mandal"}',
          );
          if (mounted) {
            context.showSuccessSnackBar('Receipt shared successfully!');
          }
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to share receipt: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Preview PDF in the app
  Future<void> _previewPdf(Wargani wargani) async {
    try {
      // Show language selection dialog
      final selectedLanguage = await _showLanguageSelectionDialog();

      // If user cancelled the dialog, return gracefully
      if (selectedLanguage == null) {
        return; // User cancelled, no need to show any message
      }

      if (!mounted) return;

      setState(() {
        _isLoading = true;
      });

      final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
        context,
        wargani,
        HiveHelper.getUsersBox().getAt(0)?.name,
        forceLanguage: selectedLanguage,
      );

      if (pdfPath != null && mounted) {
        // Share the PDF for preview (this will open it in the default PDF viewer)
        await Share.shareXFiles([XFile(pdfPath)]);

        if (mounted) {
          context.showSuccessSnackBar('PDF generated for preview');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Failed to preview PDF: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Show language selection dialog for PDF generation
  Future<String?> _showLanguageSelectionDialog() async {
    if (!mounted) return null;

    try {
      final localizations = AppLocalizations.of(context)!;

      return await showDialog<String>(
        context: context,
        barrierDismissible: true, // Allow dismissing by tapping outside
        useRootNavigator: false, // Use local navigator to prevent context issues
        builder: (BuildContext dialogContext) {
          return AlertDialog(
            title: Text(
              localizations.selectLanguage,
              style: Theme.of(dialogContext).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Select language for receipt generation:',
                  style: Theme.of(dialogContext).textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                _buildLanguageOption(dialogContext, 'en', '🇺🇸 English', 'English'),
                const SizedBox(height: 8),
                _buildLanguageOption(dialogContext, 'mr', '🇮🇳 मराठी', 'मराठी'),
                const SizedBox(height: 8),
                _buildLanguageOption(dialogContext, 'hi', '🇮🇳 हिंदी', 'हिंदी'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  if (Navigator.of(dialogContext).canPop()) {
                    Navigator.of(dialogContext).pop(null);
                  }
                },
                child: Text(localizations.cancel),
              ),
            ],
          );
        },
      );
    } catch (e) {
      print('Error showing language dialog: $e');
      return null;
    }
  }

  /// Build language option widget
  Widget _buildLanguageOption(BuildContext dialogContext, String languageCode, String flag, String name) {
    return InkWell(
      onTap: () {
        if (Navigator.of(dialogContext).canPop()) {
          Navigator.of(dialogContext).pop(languageCode);
        }
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(dialogContext).colorScheme.outline.withValues(alpha: 0.3),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Text(
              flag,
              style: const TextStyle(fontSize: 20),
            ),
            const SizedBox(width: 12),
            Text(
              name,
              style: Theme.of(dialogContext).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
