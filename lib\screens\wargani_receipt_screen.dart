import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/core/utils/validators.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/shared/widgets/custom_button.dart';
import 'package:wargani/shared/widgets/custom_card.dart';
import 'package:wargani/shared/widgets/custom_text_field.dart';
import 'package:wargani/shared/widgets/loading_overlay.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:wargani/widgets/footer.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:share_plus/share_plus.dart';

// Minor change to trigger re-save and pick up new localizations
class WarganiReceiptScreen extends StatefulWidget {
  const WarganiReceiptScreen({super.key});

  @override
  _WarganiReceiptScreenState createState() => _WarganiReceiptScreenState();
}

class _WarganiReceiptScreenState extends State<WarganiReceiptScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _receiptNoController = TextEditingController();
  final _prefixController = TextEditingController();
  final _mobileNoController = TextEditingController();
  final _registrationNoController = TextEditingController();
  final _amountInWordsController = TextEditingController();
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _receiptNoController.text =
        (HiveHelper.getWarganiBox().length + 1).toString();
  }

  void _clearForm() {
    _formKey.currentState?.reset();
    _nameController.clear();
    _amountController.clear();
    _prefixController.clear();
    _mobileNoController.clear();
    _registrationNoController.clear();
    _amountInWordsController.clear();
    setState(() {
      _receiptNoController.text =
          (HiveHelper.getWarganiBox().length + 1).toString();
      _selectedDate = DateTime.now();
    });
  }

  Future<void> _saveReceipt() async {
    if (_formKey.currentState!.validate()) {
      final wargani = Wargani(
        receiptNo: int.parse(_receiptNoController.text),
        name: _nameController.text,
        amount: double.parse(_amountController.text),
        date: _selectedDate,
        prefix: _prefixController.text,
        mobileNo: _mobileNoController.text.isNotEmpty
            ? _mobileNoController.text
            : null,
        registrationNo: _registrationNoController.text,
        amountInWords: _amountInWordsController.text,
      );
      await HiveHelper.getWarganiBox().add(wargani);
      _clearForm();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                AppLocalizations.of(context)!.pdfGeneratedSuccessfully)),
      );
    }
  }

  Future<void> _showLanguageSelectionAndDownload() async {
    if (_formKey.currentState!.validate()) {
      final wargani = Wargani(
        receiptNo: int.parse(_receiptNoController.text),
        name: _nameController.text,
        amount: double.parse(_amountController.text),
        date: _selectedDate,
        prefix: _prefixController.text,
        mobileNo: _mobileNoController.text,
        registrationNo: _registrationNoController.text,
        amountInWords: _amountInWordsController.text,
      );

      // Save the receipt first
      HiveHelper.getWarganiBox().add(wargani);

      // Show language selection dialog
      final selectedLanguage = await showDialog<String>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Select Language / भाषा निवडा'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  title: const Text('English'),
                  onTap: () => Navigator.of(context).pop('en'),
                ),
                ListTile(
                  title: const Text('मराठी (Marathi)'),
                  onTap: () => Navigator.of(context).pop('mr'),
                ),
                ListTile(
                  title: const Text('हिंदी (Hindi)'),
                  onTap: () => Navigator.of(context).pop('hi'),
                ),
              ],
            ),
          );
        },
      );

      if (selectedLanguage != null) {
        await _downloadPdfWithLanguage(wargani, selectedLanguage);
      }

      _clearForm();
    }
  }

  Future<void> _downloadPdfWithLanguage(Wargani wargani, String language) async {
    final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
        context, wargani, HiveHelper.getUsersBox().getAt(0)?.name,
        forceLanguage: language);
    if (pdfPath != null) {
      // For web, this will trigger download
      // For mobile, this will save to downloads
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Receipt downloaded successfully!')),
      );
    }
  }

  Future<void> _downloadPdf(Wargani wargani) async {
    // Show language selection dialog
    final selectedLanguage = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Language / भाषा निवडा'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('English'),
                onTap: () => Navigator.of(context).pop('en'),
              ),
              ListTile(
                title: const Text('मराठी (Marathi)'),
                onTap: () => Navigator.of(context).pop('mr'),
              ),
              ListTile(
                title: const Text('हिंदी (Hindi)'),
                onTap: () => Navigator.of(context).pop('hi'),
              ),
            ],
          ),
        );
      },
    );

    if (selectedLanguage != null && mounted) {
      final pdfPath = await PdfGenerator.generateProfessionalWarganiReceipt(
          context, wargani, HiveHelper.getUsersBox().getAt(0)?.name,
          forceLanguage: selectedLanguage);
      if (pdfPath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Receipt ${wargani.receiptNo} downloaded successfully!')),
        );
      }
    }
  }

  Future<void> _sharePdf(Wargani wargani) async {
    final pdfPath = await PdfGenerator.generate(
        context, wargani, HiveHelper.getUsersBox().getAt(0)?.name);
    if (pdfPath != null) {
      // Share via WhatsApp with the PDF
      await Share.shareXFiles(
        [XFile(pdfPath)],
        text: 'Receipt No: ${wargani.receiptNo} - ${wargani.name} - ₹${wargani.amount}',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverAppBar(
              expandedHeight: 200.0,
              floating: false,
              pinned: true,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(localizations.warganiReceipt),
                background: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: ListView(
                      children: [
                        TextFormField(
                          controller: _receiptNoController,
                          decoration: InputDecoration(
                              labelText: localizations.receiptNo),
                          keyboardType: TextInputType.number,
                          readOnly: true,
                        ),
                        TextFormField(
                          controller: _nameController,
                          decoration:
                              InputDecoration(labelText: localizations.name),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return localizations.pleaseEnterName;
                            }
                            return null;
                          },
                        ),
                        TextFormField(
                          controller: _amountController,
                          decoration: InputDecoration(
                              labelText: localizations.amount),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return localizations.pleaseEnterAmount;
                            }
                            if (double.tryParse(value) == null) {
                              return localizations.pleaseEnterValidAmount;
                            }
                            return null;
                          },
                        ),
                        TextFormField(
                          controller: _amountInWordsController,
                          decoration: InputDecoration(
                              labelText: localizations.amountInWords),
                        ),
                        TextFormField(
                          controller: _prefixController,
                          decoration: InputDecoration(
                              labelText: localizations.prefix),
                        ),
                        TextFormField(
                          controller: _mobileNoController,
                          decoration: InputDecoration(
                              labelText: localizations.mobileNo),
                          keyboardType: TextInputType.phone,
                        ),
                        TextFormField(
                          controller: _registrationNoController,
                          decoration: InputDecoration(
                              labelText: localizations.registrationNo),
                        ),
                        ListTile(
                          title: Text(
                              '${localizations.date}: ${DateFormat('dd/MM/yyyy').format(_selectedDate)}'),
                          trailing: const Icon(Icons.calendar_today),
                          onTap: () async {
                            final DateTime? picked = await showDatePicker(
                              context: context,
                              initialDate: _selectedDate,
                              firstDate: DateTime(2000),
                              lastDate: DateTime(2101),
                            );
                            if (picked != null && picked != _selectedDate) {
                              setState(() {
                                _selectedDate = picked;
                              });
                            }
                          },
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            ElevatedButton.icon(
                              onPressed: _showLanguageSelectionAndDownload,
                              icon: const Icon(Icons.download),
                              label: const Text('Download'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                              ),
                            ),
                            ElevatedButton.icon(
                              onPressed: () async {
                                if (_formKey.currentState!.validate()) {
                                  final wargani = Wargani(
                                    receiptNo: int.parse(_receiptNoController.text),
                                    name: _nameController.text,
                                    amount: double.parse(_amountController.text),
                                    date: _selectedDate,
                                    prefix: _prefixController.text,
                                    mobileNo: _mobileNoController.text,
                                    registrationNo: _registrationNoController.text,
                                    amountInWords: _amountInWordsController.text,
                                  );
                                  HiveHelper.getWarganiBox().add(wargani);
                                  await _sharePdf(wargani);
                                  _clearForm();
                                }
                              },
                              icon: const Icon(Icons.share),
                              label: const Text('Share'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                              ),
                            ),
                            ElevatedButton.icon(
                              onPressed: () async {
                                if (_formKey.currentState!.validate()) {
                                  final currentContext = context;
                                  final wargani = Wargani(
                                    receiptNo: int.parse(_receiptNoController.text),
                                    name: _nameController.text,
                                    amount: double.parse(_amountController.text),
                                    date: _selectedDate,
                                    prefix: _prefixController.text,
                                    mobileNo: _mobileNoController.text,
                                    registrationNo: _registrationNoController.text,
                                    amountInWords: _amountInWordsController.text,
                                  );
                                  await PdfGenerator.generate(
                                      currentContext, wargani, HiveHelper.getUsersBox().getAt(0)?.name);
                                  // Preview will be shown in the PDF viewer
                                }
                              },
                              icon: const Icon(Icons.preview),
                              label: const Text('Preview'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        ElevatedButton(
                          onPressed: _clearForm,
                          child: Text(localizations.clearForm),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ];
        },
        body: ValueListenableBuilder(
          valueListenable: HiveHelper.getWarganiBox().listenable(),
          builder: (context, Box<Wargani> box, _) {
            final receipts = box.values.toList().cast<Wargani>();
            return ListView.builder(
              itemCount: receipts.length,
              itemBuilder: (context, index) {
                final receipt = receipts[index];
                return Card(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: ListTile(
                    title: Text(receipt.name),
                    subtitle: Text(
                        '${localizations.receiptNo}: ${receipt.receiptNo} - ₹${receipt.amount}'),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.download),
                          onPressed: () => _downloadPdf(receipt),
                        ),
                        IconButton(
                          icon: const Icon(Icons.share),
                          onPressed: () => _sharePdf(receipt),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
      bottomSheet: const Footer(),
    );
  }
}
