import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/expense_model.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:share_plus/share_plus.dart';
import 'package:wargani/utils/pdf_generator.dart';

class ExpensesScreen extends StatefulWidget {
  const ExpensesScreen({super.key});

  @override
  _ExpensesScreenState createState() => _ExpensesScreenState();
}

class _ExpensesScreenState extends State<ExpensesScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();

  void _clearForm() {
    _formKey.currentState?.reset();
    _titleController.clear();
    _amountController.clear();
    _descriptionController.clear();
  }

  void _addExpense() {
    if (_formKey.currentState!.validate()) {
      final expense = Expense(
        title: _titleController.text,
        amount: double.parse(_amountController.text),
        description: _descriptionController.text,
        date: DateTime.now(),
      );
      HiveHelper.getExpensesBox().add(expense);
      _clearForm();
      Navigator.of(context).pop();
    }
  }

  Future<void> _downloadExpense(Expense expense) async {
    final pdfPath = await PdfGenerator.generateExpenseReport(
        context, expense, HiveHelper.getUsersBox().getAt(0)?.name);
    if (pdfPath != null) {
      await Share.shareXFiles([XFile(pdfPath)]);
    }
  }

  Future<void> _shareExpense(Expense expense) async {
    final pdfPath = await PdfGenerator.generateExpenseReport(
        context, expense, HiveHelper.getUsersBox().getAt(0)?.name);
    if (pdfPath != null) {
      await Share.shareXFiles([XFile(pdfPath)]);
    }
  }

  Future<void> _downloadAllExpenses() async {
    final expenses = HiveHelper.getExpensesBox().values.toList();
    if (expenses.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No expenses to download')),
      );
      return;
    }

    // Show language selection dialog
    final selectedLanguage = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Language / भाषा निवडा'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('English'),
                onTap: () => Navigator.of(context).pop('en'),
              ),
              ListTile(
                title: const Text('मराठी (Marathi)'),
                onTap: () => Navigator.of(context).pop('mr'),
              ),
              ListTile(
                title: const Text('हिंदी (Hindi)'),
                onTap: () => Navigator.of(context).pop('hi'),
              ),
            ],
          ),
        );
      },
    );

    if (selectedLanguage != null && mounted) {
      final pdfPath = await PdfGenerator.generateExpenseBankStatement(
          context, expenses, HiveHelper.getUsersBox().getAt(0)?.name,
          forceLanguage: selectedLanguage);
      if (pdfPath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Expense statement downloaded successfully!')),
        );
      }
    }
  }

  Future<void> _shareAllExpenses() async {
    final expenses = HiveHelper.getExpensesBox().values.toList();
    if (expenses.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No expenses to share')),
      );
      return;
    }

    final pdfPath = await PdfGenerator.generateExpenseBankStatement(
        context, expenses, HiveHelper.getUsersBox().getAt(0)?.name);
    if (pdfPath != null) {
      final totalAmount = expenses.fold<double>(0, (sum, expense) => sum + expense.amount);
      await Share.shareXFiles(
        [XFile(pdfPath)],
        text: 'Expense Statement - Total: ₹${totalAmount.toStringAsFixed(2)}',
      );
    }
  }



  void _showAddExpenseDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 16,
            right: 16,
            top: 16,
          ),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _titleController,
                  decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.title),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context)!.pleaseEnterTitle;
                    }
                    return null;
                  },
                ),
                TextFormField(
                  controller: _amountController,
                  decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.amount),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context)!.pleaseEnterAmount;
                    }
                    if (double.tryParse(value) == null) {
                      return AppLocalizations.of(context)!.pleaseEnterValidNumber;
                    }
                    return null;
                  },
                ),
                TextFormField(
                  controller: _descriptionController,
                  decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.description),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: _addExpense,
                  child: Text(AppLocalizations.of(context)!.addExpense),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.expenses),
        actions: [
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _downloadAllExpenses,
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareAllExpenses,
          ),
        ],
      ),
      body: ValueListenableBuilder(
        valueListenable: HiveHelper.getExpensesBox().listenable(),
        builder: (context, Box<Expense> box, _) {
          final expenses = box.values.toList().cast<Expense>();
          if (expenses.isEmpty) {
            return Center(child: Text(localizations.noExpensesYet));
          }
          return ListView.builder(
            itemCount: expenses.length,
            itemBuilder: (context, index) {
              final expense = expenses[index];
              return Card(
                margin:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ListTile(
                  title: Text(expense.title),
                  subtitle: Text(expense.description),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text('₹${expense.amount}'),
                      IconButton(
                        icon: const Icon(Icons.download),
                        onPressed: () => _downloadExpense(expense),
                      ),
                      IconButton(
                        icon: const Icon(Icons.share),
                        onPressed: () => _shareExpense(expense),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddExpenseDialog,
        child: const Icon(Icons.add),
      ),
    );
  }
}
