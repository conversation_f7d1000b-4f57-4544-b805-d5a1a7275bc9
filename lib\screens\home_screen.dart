import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/features/dashboard/presentation/screens/enhanced_dashboard_screen.dart';
import 'package:wargani/features/donations/presentation/screens/enhanced_donations_screen.dart';
import 'package:wargani/features/expenses/presentation/screens/enhanced_expenses_screen.dart';
import 'package:wargani/features/receipts/presentation/screens/enhanced_wargani_receipt_screen.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/screens/login_screen.dart';
import 'package:wargani/screens/profile_screen.dart';
import 'package:wargani/screens/ganeshotsav_templates_screen.dart';

/// Enhanced home screen with professional navigation and animations
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  int _selectedIndex = 0;
  late PageController _pageController;
  late AnimationController _animationController;

  static const List<Widget> _screens = [
    EnhancedDashboardScreen(),
    EnhancedWarganiReceiptScreen(),
    EnhancedExpensesScreen(),
    EnhancedDonationsScreen(),
    GaneshotsavTemplatesScreen(),
    ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: AppConstants.shortAnimation,
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: AppConstants.mediumAnimation,
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        children: _screens,
      ),
      bottomNavigationBar: _buildBottomNavigationBar(localizations),
      floatingActionButton: _selectedIndex == 0
          ? _buildFloatingActionButton(localizations)
          : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  Widget _buildBottomNavigationBar(AppLocalizations localizations) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: BottomNavigationBar(
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.dashboard_rounded),
            activeIcon: const Icon(Icons.dashboard_rounded),
            label: localizations.dashboard,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.receipt_long_rounded),
            activeIcon: const Icon(Icons.receipt_long_rounded),
            label: localizations.warganiReceipt,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.money_off_rounded),
            activeIcon: const Icon(Icons.money_off_rounded),
            label: localizations.expenses,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.volunteer_activism_rounded),
            activeIcon: const Icon(Icons.volunteer_activism_rounded),
            label: localizations.donations,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.temple_hindu_rounded),
            activeIcon: const Icon(Icons.temple_hindu_rounded),
            label: 'Templates',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.person_rounded),
            activeIcon: const Icon(Icons.person_rounded),
            label: localizations.profile,
          ),
        ],
        currentIndex: _selectedIndex,
        type: BottomNavigationBarType.fixed,
        onTap: _onItemTapped,
        elevation: 0,
        selectedFontSize: 12,
        unselectedFontSize: 10,
      ),
    ).animate().slideY(begin: 1, end: 0, duration: 600.ms, curve: Curves.easeOut);
  }

  Widget _buildFloatingActionButton(AppLocalizations localizations) {
    return FloatingActionButton.extended(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const EnhancedWarganiReceiptScreen(),
          ),
        );
      },
      icon: const Icon(Icons.add_rounded),
      label: const Text('New Receipt'),
      backgroundColor: context.colorScheme.primary,
      foregroundColor: Colors.white,
    ).animate().scale(delay: 800.ms, duration: 400.ms, curve: Curves.elasticOut);
  }

  // Method to handle logout
  void _handleLogout() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final localizations = AppLocalizations.of(context)!;
        return AlertDialog(
          title: Text(localizations.logout),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(localizations.cancel),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const LoginScreen(),
                  ),
                  (route) => false,
                );
              },
              child: Text(localizations.logout),
            ),
          ],
        );
      },
    );
  }
}
