import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/screens/donations_screen.dart';
import 'package:wargani/screens/expenses_screen.dart';
import 'package:wargani/screens/profile_screen.dart';
import 'package:wargani/screens/wargani_receipt_screen.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/widgets/dashboard_card.dart';
import 'package:wargani/widgets/footer.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  _DashboardScreenState createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {

  void _showUpiQrCode(BuildContext context, Uint8List qrCodeBytes) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.orange.shade50,
                  Colors.white,
                ],
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Row(
                  children: [
                    Icon(
                      Icons.qr_code_2,
                      color: Colors.orange.shade700,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'UPI Payment QR Code',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      color: Colors.grey.shade600,
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // QR Code with enhanced styling
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.orange.shade200, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.orange.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Image.memory(
                    qrCodeBytes,
                    height: 280,
                    width: 280,
                    fit: BoxFit.contain,
                  ),
                ),
                const SizedBox(height: 20),

                // Instructions
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: const Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.blue,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Open any UPI app and scan this QR code to make payment',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.blue,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close, size: 18),
                        label: const Text('Close'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.grey.shade700,
                          side: BorderSide(color: Colors.grey.shade300),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).pop();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('QR Code ready for scanning!'),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 2),
                            ),
                          );
                        },
                        icon: const Icon(Icons.check, size: 18),
                        label: const Text('Ready'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final warganiBox = HiveHelper.getWarganiBox();
    final donationsBox = HiveHelper.getDonationsBox();
    final expensesBox = HiveHelper.getExpensesBox();
    final profileBox = HiveHelper.getProfileBox();

    final totalWargani = warganiBox.values.fold<double>(0, (sum, item) => sum + item.amount);
    final totalDonations = donationsBox.values.fold<double>(0, (sum, item) => sum + item.amount);
    final totalExpenses = expensesBox.values.fold<double>(0, (sum, item) => sum + item.amount);
    final totalPeople = warganiBox.length;
    final netBalance = (totalWargani + totalDonations) - totalExpenses; // Calculate net balance

    // Get profile for logos and UPI QR code
    final profile = profileBox.isNotEmpty ? profileBox.getAt(0) : null;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            // Left logo
            if (profile?.leftLogoBytes != null) ...[
              Container(
                height: 30,
                width: 30,
                margin: const EdgeInsets.only(right: 8),
                child: Image.memory(profile!.leftLogoBytes!),
              ),
            ],
            Expanded(child: Text(localizations.dashboard)),
            // Right logo
            if (profile?.rightLogoBytes != null) ...[
              Container(
                height: 30,
                width: 30,
                margin: const EdgeInsets.only(left: 8),
                child: Image.memory(profile!.rightLogoBytes!),
              ),
            ],
          ],
        ),
        actions: [
          // UPI QR Code button
          if (profile?.upiQrCodeBytes != null)
            IconButton(
              icon: const Icon(Icons.qr_code),
              tooltip: 'UPI QR Code',
              onPressed: () => _showUpiQrCode(context, profile!.upiQrCodeBytes!),
            ),
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ProfileScreen()),
              ).then((_) => setState(() {}));
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // UPI QR Code Quick Access Section
          if (profile?.upiQrCodeBytes != null) ...[
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  // QR Code Image
                  Container(
                    height: 80,
                    width: 80,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.orange.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.memory(
                        profile!.upiQrCodeBytes!,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // QR Code Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'UPI Payment QR Code',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange,
                          ),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Tap to view full size QR code for payments',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: () => _showUpiQrCode(context, profile!.upiQrCodeBytes!),
                          icon: const Icon(Icons.qr_code_scanner, size: 16),
                          label: const Text('View QR Code'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            textStyle: const TextStyle(fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],

          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  DashboardCard(
                    title: localizations.warganiSummary,
                    subtitle:
                        '${localizations.totalPeople}: $totalPeople\n${localizations.totalAmount}: $totalWargani',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const WarganiReceiptScreen()),
                      ).then((_) => setState(() {}));
                    },
                    icon: Icons.people,
                    color: Colors.blue.shade100,
                  ),
                  DashboardCard(
                    title: localizations.donationSummary,
                    subtitle:
                        '${localizations.totalAmount}: $totalDonations',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const DonationsScreen()),
                      ).then((_) => setState(() {}));
                    },
                    icon: Icons.volunteer_activism,
                    color: Colors.green.shade100,
                  ),
                  DashboardCard(
                    title: localizations.expensesSummary,
                    subtitle:
                        '${localizations.totalAmount}: $totalExpenses',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const ExpensesScreen()),
                      ).then((_) => setState(() {}));
                    },
                    icon: Icons.money_off,
                    color: Colors.red.shade100,
                  ),
                  DashboardCard(
                    title: localizations.netBalance, // New card for Net Balance
                    subtitle: '${localizations.totalAmount}: $netBalance',
                    onTap: () {
                      // No specific screen for net balance, maybe show a dialog or just refresh
                    },
                    icon: Icons.account_balance_wallet,
                    color: Colors.purple.shade100,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      // Floating Action Button for Quick QR Access
      floatingActionButton: profile?.upiQrCodeBytes != null
          ? FloatingActionButton.extended(
              onPressed: () => _showUpiQrCode(context, profile!.upiQrCodeBytes!),
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              icon: const Icon(Icons.qr_code_scanner),
              label: const Text('QR Pay'),
              tooltip: 'Quick UPI QR Code Access',
            )
          : null,
    );
  }
}
