import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:intl/intl.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:share_plus/share_plus.dart';

class BinFolderScreen extends StatefulWidget {
  const BinFolderScreen({super.key});

  @override
  State<BinFolderScreen> createState() => _BinFolderScreenState();
}

class _BinFolderScreenState extends State<BinFolderScreen> {
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Icon(Icons.delete_rounded),
            const SizedBox(width: 8),
            Text(localizations.binFolder),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.download_rounded),
            tooltip: 'Download Deleted Report',
            onPressed: () => _downloadDeletedReport(),
          ),
          IconButton(
            icon: const Icon(Icons.share_rounded),
            tooltip: 'Share Deleted Report',
            onPressed: () => _shareDeletedReport(),
          ),
        ],
      ),
      body: ValueListenableBuilder(
        valueListenable: HiveHelper.getDeletedWarganiBox().listenable(),
        builder: (context, Box<Wargani> box, _) {
          final deletedReceipts = box.values.toList().cast<Wargani>();

          if (deletedReceipts.isEmpty) {
            return _buildEmptyState(localizations);
          }

          return Column(
            children: [
              // Header with count
              Container(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${localizations.deletedReceipts} (${deletedReceipts.length})',
                        style: context.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Text(
                      'View Only - No Restore',
                      style: context.textTheme.bodySmall?.copyWith(
                        color: context.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
              // Deleted receipts list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                  itemCount: deletedReceipts.length,
                  itemBuilder: (context, index) {
                    final receipt = deletedReceipts[index];
                    return _buildDeletedReceiptCard(receipt, localizations, index);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.delete_outline_rounded,
            size: 64,
            color: context.colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            localizations.binEmpty,
            style: context.textTheme.titleLarge?.copyWith(
              color: context.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            localizations.binEmptyDescription,
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDeletedReceiptCard(Wargani receipt, AppLocalizations localizations, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${localizations.receiptNo} ${receipt.receiptNo}',
                    style: context.textTheme.labelSmall?.copyWith(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  DateFormat('dd/MM/yyyy').format(receipt.date),
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '${receipt.prefix} ${receipt.name}',
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Amount',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: context.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                      Text(
                        '₹${receipt.amount.toStringAsFixed(2)}',
                        style: context.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ),
                if (receipt.mobileNo != null && receipt.mobileNo!.isNotEmpty)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Mobile',
                          style: context.textTheme.bodySmall?.copyWith(
                            color: context.colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                        Text(
                          receipt.mobileNo!,
                          style: context.textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                if (receipt.paymentMethod != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: context.colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      receipt.paymentMethod!,
                      style: context.textTheme.labelSmall,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            // View only - no restore option as per requirement
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'Deleted Receipt - View Only',
                style: context.textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadDeletedReport() async {
    try {
      final deletedBox = HiveHelper.getDeletedWarganiBox();
      final deletedReceipts = deletedBox.values.toList().cast<Wargani>();

      if (deletedReceipts.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No deleted receipts to download')),
          );
        }
        return;
      }

      final pdfPath = await PdfGenerator.generateDeletedReceiptsReport(context, deletedReceipts);

      if (pdfPath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Deleted receipts report downloaded successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to download report: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _shareDeletedReport() async {
    try {
      final deletedBox = HiveHelper.getDeletedWarganiBox();
      final deletedReceipts = deletedBox.values.toList().cast<Wargani>();

      if (deletedReceipts.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No deleted receipts to share')),
          );
        }
        return;
      }

      final pdfPath = await PdfGenerator.generateDeletedReceiptsReport(context, deletedReceipts);

      if (pdfPath != null) {
        await Share.shareXFiles([XFile(pdfPath)], text: 'Deleted Receipts Report');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share report: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
