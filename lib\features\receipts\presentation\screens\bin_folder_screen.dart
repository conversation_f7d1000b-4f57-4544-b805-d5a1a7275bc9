import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:intl/intl.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/wargani_model.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/widgets/custom_button.dart';

class BinFolderScreen extends StatefulWidget {
  const BinFolderScreen({super.key});

  @override
  State<BinFolderScreen> createState() => _BinFolderScreenState();
}

class _BinFolderScreenState extends State<BinFolderScreen> {
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Icon(Icons.delete_rounded),
            const SizedBox(width: 8),
            Text(localizations.binFolder),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_forever_rounded),
            tooltip: localizations.emptyBin,
            onPressed: () => _showEmptyBinDialog(),
          ),
        ],
      ),
      body: ValueListenableBuilder(
        valueListenable: HiveHelper.getDeletedWarganiBox().listenable(),
        builder: (context, Box<Wargani> box, _) {
          final deletedReceipts = box.values.toList().cast<Wargani>();

          if (deletedReceipts.isEmpty) {
            return _buildEmptyState(localizations);
          }

          return Column(
            children: [
              // Header with count
              Container(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${localizations.deletedReceipts} (${deletedReceipts.length})',
                        style: context.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Text(
                      localizations.tapToRestore,
                      style: context.textTheme.bodySmall?.copyWith(
                        color: context.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
              // Deleted receipts list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                  itemCount: deletedReceipts.length,
                  itemBuilder: (context, index) {
                    final receipt = deletedReceipts[index];
                    return _buildDeletedReceiptCard(receipt, localizations, index);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.delete_outline_rounded,
            size: 64,
            color: context.colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            localizations.binEmpty,
            style: context.textTheme.titleLarge?.copyWith(
              color: context.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            localizations.binEmptyDescription,
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDeletedReceiptCard(Wargani receipt, AppLocalizations localizations, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: InkWell(
        onTap: () => _showRestoreDialog(receipt),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '${localizations.receiptNo} ${receipt.receiptNo}',
                      style: context.textTheme.labelSmall?.copyWith(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    DateFormat('dd/MM/yyyy').format(receipt.date),
                    style: context.textTheme.bodySmall?.copyWith(
                      color: context.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.person_rounded,
                    size: 16,
                    color: context.colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '${receipt.prefix} ${receipt.name}',
                      style: context.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.currency_rupee_rounded,
                    size: 16,
                    color: context.colorScheme.onSurface.withOpacity(0.6),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    receipt.amount.toCurrency,
                    style: context.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: context.colorScheme.primary,
                    ),
                  ),
                  const Spacer(),
                  if (receipt.paymentMethod != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: context.colorScheme.surfaceVariant,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        receipt.paymentMethod!,
                        style: context.textTheme.labelSmall,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: localizations.restore,
                      variant: ButtonVariant.secondary,
                      size: ButtonSize.small,
                      icon: Icons.restore_rounded,
                      onPressed: () => _restoreReceipt(receipt),
                    ),
                  ),
                  const SizedBox(width: 8),
                  CustomButton(
                    text: '',
                    variant: ButtonVariant.secondary,
                    size: ButtonSize.small,
                    icon: Icons.delete_forever_rounded,
                    onPressed: () => _permanentlyDeleteReceipt(receipt),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showRestoreDialog(Wargani receipt) {
    final localizations = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.restoreReceipt),
        content: Text(
          localizations.restoreReceiptConfirmation(receipt.receiptNo.toString()),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _restoreReceipt(receipt);
            },
            child: Text(localizations.restore),
          ),
        ],
      ),
    );
  }

  void _restoreReceipt(Wargani receipt) async {
    final localizations = AppLocalizations.of(context)!;
    try {
      // Add back to main wargani box
      final warganiBox = HiveHelper.getWarganiBox();
      await warganiBox.add(receipt);

      // Remove from deleted box
      final deletedBox = HiveHelper.getDeletedWarganiBox();
      final receiptKey = deletedBox.keys.firstWhere(
        (key) => deletedBox.get(key)?.receiptNo == receipt.receiptNo,
        orElse: () => null,
      );
      
      if (receiptKey != null) {
        await deletedBox.delete(receiptKey);
      }

      if (mounted) {
        context.showSuccessSnackBar(
          localizations.receiptRestored(receipt.receiptNo.toString())
        );
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar(localizations.failedToRestoreReceipt(e.toString()));
      }
    }
  }

  void _permanentlyDeleteReceipt(Wargani receipt) async {
    final localizations = AppLocalizations.of(context)!;
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.permanentlyDelete),
        content: Text(
          localizations.permanentlyDeleteConfirmation(receipt.receiptNo.toString()),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(localizations.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final deletedBox = HiveHelper.getDeletedWarganiBox();
        final receiptKey = deletedBox.keys.firstWhere(
          (key) => deletedBox.get(key)?.receiptNo == receipt.receiptNo,
          orElse: () => null,
        );
        
        if (receiptKey != null) {
          await deletedBox.delete(receiptKey);
        }

        if (mounted) {
          context.showSuccessSnackBar(
            localizations.receiptPermanentlyDeleted(receipt.receiptNo.toString())
          );
        }
      } catch (e) {
        if (mounted) {
          context.showErrorSnackBar(localizations.failedToDeleteReceipt(e.toString()));
        }
      }
    }
  }

  void _showEmptyBinDialog() {
    final localizations = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.emptyBin),
        content: Text(localizations.emptyBinConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _emptyBin();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(localizations.emptyBin),
          ),
        ],
      ),
    );
  }

  void _emptyBin() async {
    final localizations = AppLocalizations.of(context)!;
    try {
      final deletedBox = HiveHelper.getDeletedWarganiBox();
      await deletedBox.clear();

      if (mounted) {
        context.showSuccessSnackBar(localizations.binEmptied);
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar(localizations.failedToEmptyBin(e.toString()));
      }
    }
  }
}
