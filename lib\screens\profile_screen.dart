import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/profile_model.dart';
import 'package:wargani/models/user_model.dart'; // Import UserModel
import 'package:wargani/utils/hive_helper.dart';
import 'package:wargani/widgets/footer.dart';
import 'package:wargani/main.dart'; // Import WarganiApp
import 'package:wargani/screens/login_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mandalNameController = TextEditingController();
  final _addressController = TextEditingController();
  final _currentYearController = TextEditingController();
  final _mandalRegistrationNoController = TextEditingController();
  final _festivalNameController = TextEditingController();
  final _customHeaderTextController = TextEditingController();
  final _leftHeaderTextController = TextEditingController();
  final _middleHeaderTextController = TextEditingController();
  final _rightHeaderTextController = TextEditingController();
  final _customFestivalNameController = TextEditingController();
  Uint8List? _leftLogoBytes;
  Uint8List? _rightLogoBytes;
  Uint8List? _upiQrCodeBytes;
  User? _currentUser;
  bool _isEditing = false; // Add isEditing state

  @override
  void initState() {
    super.initState();
    _loadProfile();
    _loadCurrentUser();
  }

  void _loadCurrentUser() {
    // Assuming HiveHelper has a method to get the current logged-in user
    // For now, I'll just get the first user in the box as a placeholder.
    // In a real app, you'd have a proper login state management.
    final userBox = HiveHelper.getUsersBox();
    if (userBox.isNotEmpty) {
      setState(() {
        _currentUser = userBox.getAt(0); // Get the first user as current user
      });
    }
  }

  void _loadProfile() {
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isNotEmpty) {
      final profile = profileBox.getAt(0)!;
      _mandalNameController.text = profile.mandalName;
      _addressController.text = profile.address;
      _currentYearController.text = profile.currentYear;
      _mandalRegistrationNoController.text = profile.mandalRegistrationNo ?? '';
      _festivalNameController.text = profile.festivalName ?? 'गणेशोत्सव';
      _customHeaderTextController.text = profile.customHeaderText ?? '|| श्री गणेश प्रसन्न ||';
      _leftHeaderTextController.text = profile.leftHeaderText ?? '';
      _middleHeaderTextController.text = profile.middleHeaderText ?? '';
      _rightHeaderTextController.text = profile.rightHeaderText ?? '';
      _customFestivalNameController.text = profile.customFestivalName ?? 'गणेशोत्सव';

      setState(() {
        _leftLogoBytes = profile.leftLogoBytes;
        _rightLogoBytes = profile.rightLogoBytes;
        _upiQrCodeBytes = profile.upiQrCodeBytes;
      });
    }
  }

  Future<void> _pickLeftLogo() async {
    final pickedFile =
        await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      final bytes = await pickedFile.readAsBytes();
      setState(() {
        _leftLogoBytes = bytes;
      });
    }
  }

  Future<void> _pickRightLogo() async {
    final pickedFile =
        await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      final bytes = await pickedFile.readAsBytes();
      setState(() {
        _rightLogoBytes = bytes;
      });
    }
  }

  Future<void> _pickUpiQrCode() async {
    final pickedFile =
        await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      final bytes = await pickedFile.readAsBytes();
      setState(() {
        _upiQrCodeBytes = bytes;
      });
    }
  }

  void _saveProfile() {
    if (_formKey.currentState!.validate()) {
      final localizations = AppLocalizations.of(context)!;
      final profile = Profile(
        mandalName: _mandalNameController.text,
        address: _addressController.text,
        currentYear: _currentYearController.text,
        logoBytes: null, // Remove main logo field
        mandalRegistrationNo: _mandalRegistrationNoController.text,
        festivalName: _festivalNameController.text,
        leftLogoBytes: _leftLogoBytes,
        rightLogoBytes: _rightLogoBytes,
        customHeaderText: _customHeaderTextController.text,
        leftHeaderText: _leftHeaderTextController.text,
        middleHeaderText: _middleHeaderTextController.text,
        rightHeaderText: _rightHeaderTextController.text,
        upiQrCodeBytes: _upiQrCodeBytes,
        customFestivalName: _customFestivalNameController.text,
      );
      final profileBox = HiveHelper.getProfileBox();
      if (profileBox.isNotEmpty) {
        profileBox.putAt(0, profile);
      } else {
        profileBox.add(profile);
      }

      setState(() {
        _isEditing = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localizations.profileSaved)),
      );

      _showProfileSavedDialog(profile);
    }
  }

  void _showProfileSavedDialog(Profile profile) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            SizedBox(width: 10),
            Text('Profile Saved!'),
          ],
        ),
        content: Container(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (profile.logoBytes != null) ...[
                Center(
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.orange, width: 2),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.memory(
                        profile.logoBytes!,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 15),
              ],
              _buildProfileDetailRow('Mandal Name:', profile.mandalName),
              _buildProfileDetailRow('Address:', profile.address),
              _buildProfileDetailRow('Year:', profile.currentYear),
              if (profile.mandalRegistrationNo != null && profile.mandalRegistrationNo!.isNotEmpty)
                _buildProfileDetailRow('Registration No:', profile.mandalRegistrationNo!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.orange.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSavedProfileCard() {
    final profileBox = HiveHelper.getProfileBox();
    if (profileBox.isEmpty) {
      return Card(
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(Icons.info_outline, size: 48, color: Colors.grey),
              SizedBox(height: 10),
              Text(
                'No Profile Saved',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade600,
                ),
              ),
              SizedBox(height: 5),
              Text(
                'Please fill and save your mandal profile below',
                style: TextStyle(color: Colors.grey.shade600),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    final profile = profileBox.getAt(0)!;
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.orange.shade50, Colors.orange.shade100],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.verified, color: Colors.green, size: 24),
                  SizedBox(width: 8),
                  Text(
                    'Saved Profile',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange.shade800,
                    ),
                  ),
                  Spacer(),
                  IconButton(
                    icon: Icon(Icons.edit, color: Colors.orange.shade700),
                    onPressed: () {
                      setState(() {
                        _isEditing = true;
                      });
                    },
                  ),
                ],
              ),
              SizedBox(height: 15),

              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Logo
                  if (profile.logoBytes != null) ...[
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(color: Colors.orange, width: 2),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.orange.withOpacity(0.3),
                            blurRadius: 8,
                            offset: Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.memory(
                          profile.logoBytes!,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    SizedBox(width: 15),
                  ],

                  // Profile Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSavedProfileRow('Mandal:', profile.mandalName),
                        _buildSavedProfileRow('Address:', profile.address),
                        _buildSavedProfileRow('Year:', profile.currentYear),
                        if (profile.mandalRegistrationNo != null && profile.mandalRegistrationNo!.isNotEmpty)
                          _buildSavedProfileRow('Reg. No:', profile.mandalRegistrationNo!),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSavedProfileRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 70,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.orange.shade700,
                fontSize: 13,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.profile),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Display Saved Profile Card
                    _buildSavedProfileCard(),
                    const SizedBox(height: 20),

                    // Display User Profile Information
                    if (_currentUser != null) ...[
                      TextFormField(
                        initialValue: _currentUser!.name,
                        decoration: InputDecoration(labelText: localizations.userName),
                        readOnly: true,
                      ),
                      const SizedBox(height: 10),
                      TextFormField(
                        initialValue: _currentUser!.email,
                        decoration: InputDecoration(labelText: localizations.userEmail),
                        readOnly: true,
                      ),
                      const SizedBox(height: 20),
                    ],
                    TextFormField(
                      controller: _mandalNameController,
                      decoration:
                          InputDecoration(labelText: localizations.mandalName),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localizations.pleaseEnterMandalName;
                        }
                        return null;
                      },
                      readOnly: !_isEditing,
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      controller: _mandalRegistrationNoController,
                      decoration: InputDecoration(
                          labelText: localizations.registrationNo),
                      readOnly: !_isEditing,
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      controller: _addressController,
                      decoration:
                          InputDecoration(labelText: localizations.address),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localizations.pleaseEnterAddress;
                        }
                        return null;
                      },
                      readOnly: !_isEditing,
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      controller: _currentYearController,
                      decoration:
                          InputDecoration(labelText: localizations.currentYear),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localizations.pleaseEnterCurrentYear;
                        }
                        return null;
                      },
                      readOnly: !_isEditing,
                    ),
                    const SizedBox(height: 20),

                    // Custom Festival Name
                    TextFormField(
                      controller: _customFestivalNameController,
                      decoration: const InputDecoration(
                        labelText: 'Festival Name (e.g., गणेशोत्सव)',
                        hintText: 'Enter custom festival name',
                      ),
                      readOnly: !_isEditing,
                    ),
                    const SizedBox(height: 10),

                    // Header Text Fields
                    const Text(
                      'Receipt Header Text (3 positions):',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      controller: _leftHeaderTextController,
                      decoration: const InputDecoration(
                        labelText: 'Left Header Text',
                        hintText: 'Text for left side of receipt header',
                      ),
                      readOnly: !_isEditing,
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      controller: _middleHeaderTextController,
                      decoration: const InputDecoration(
                        labelText: 'Middle Header Text',
                        hintText: 'Text for center of receipt header',
                      ),
                      readOnly: !_isEditing,
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      controller: _rightHeaderTextController,
                      decoration: const InputDecoration(
                        labelText: 'Right Header Text',
                        hintText: 'Text for right side of receipt header',
                      ),
                      readOnly: !_isEditing,
                    ),
                    const SizedBox(height: 20),

                    // Logo Section
                    const Text(
                      'Logos:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        _leftLogoBytes == null
                            ? const Text('No left logo selected.')
                            : Image.memory(_leftLogoBytes!, height: 80),
                        const SizedBox(width: 20),
                        ElevatedButton(
                          onPressed: _isEditing ? _pickLeftLogo : null,
                          child: const Text('Left Logo'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        _rightLogoBytes == null
                            ? const Text('No right logo selected.')
                            : Image.memory(_rightLogoBytes!, height: 80),
                        const SizedBox(width: 20),
                        ElevatedButton(
                          onPressed: _isEditing ? _pickRightLogo : null,
                          child: const Text('Right Logo'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // UPI QR Code Section
                    const Text(
                      'UPI QR Code:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        _upiQrCodeBytes == null
                            ? const Text('No UPI QR code selected.')
                            : Image.memory(_upiQrCodeBytes!, height: 120),
                        const SizedBox(width: 20),
                        ElevatedButton(
                          onPressed: _isEditing ? _pickUpiQrCode : null,
                          child: const Text('UPI QR Code'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    if (_isEditing)
                      ElevatedButton(
                        onPressed: () {
                          _saveProfile();
                          setState(() {
                            _isEditing = false;
                          });
                        },
                        child: Text(localizations.save),
                      ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _isEditing = true;
                        });
                      },
                      child: const Text('Edit'),
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pushReplacement(
                          MaterialPageRoute(
                            builder: (context) => const LoginScreen(),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                      ),
                      child: Text(localizations.logout),
                    ),

                  ],
                ),
              ),
            ),
          ),
          const Column(
            children: [
              Text(
                'Developed by AMSSoftX Web : https://amssoftx.com',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 12),
              ),
              Text(
                'V1.1.0',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
