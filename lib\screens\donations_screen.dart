import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/models/donation_model.dart';
import 'package:wargani/utils/hive_helper.dart';
import 'package:share_plus/share_plus.dart';
import 'package:wargani/utils/pdf_generator.dart';

class DonationsScreen extends StatefulWidget {
  const DonationsScreen({super.key});

  @override
  _DonationsScreenState createState() => _DonationsScreenState();
}

class _DonationsScreenState extends State<DonationsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _donorNameController = TextEditingController();
  final _amountController = TextEditingController();
  final _reasonController = TextEditingController();

  void _clearForm() {
    _formKey.currentState?.reset();
    _donorNameController.clear();
    _amountController.clear();
    _reasonController.clear();
  }

  void _addDonation() {
    if (_formKey.currentState!.validate()) {
      final donation = Donation(
        donorName: _donorNameController.text,
        amount: double.parse(_amountController.text),
        reason: _reasonController.text,
        date: DateTime.now(),
      );
      HiveHelper.getDonationsBox().add(donation);
      _clearForm();
      Navigator.of(context).pop();
    }
  }

  Future<void> _downloadDonation(Donation donation) async {
    // Show language selection dialog
    final selectedLanguage = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Language / भाषा निवडा'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('English'),
                onTap: () => Navigator.of(context).pop('en'),
              ),
              ListTile(
                title: const Text('मराठी (Marathi)'),
                onTap: () => Navigator.of(context).pop('mr'),
              ),
              ListTile(
                title: const Text('हिंदी (Hindi)'),
                onTap: () => Navigator.of(context).pop('hi'),
              ),
            ],
          ),
        );
      },
    );

    if (selectedLanguage != null && mounted) {
      final pdfPath = await PdfGenerator.generateDonationReceipt(
          context, donation, HiveHelper.getUsersBox().getAt(0)?.name);
      if (pdfPath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Donation by ${donation.donorName} downloaded successfully!')),
        );
      }
    }
  }

  Future<void> _shareDonation(Donation donation) async {
    final pdfPath = await PdfGenerator.generateDonationReceipt(
        context, donation, HiveHelper.getUsersBox().getAt(0)?.name);
    if (pdfPath != null) {
      await Share.shareXFiles([XFile(pdfPath)]);
    }
  }

  void _showAddDonationDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 16,
            right: 16,
            top: 16,
          ),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _donorNameController,
                  decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.donorName),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context)!.pleaseEnterDonorName;
                    }
                    return null;
                  },
                ),
                TextFormField(
                  controller: _amountController,
                  decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.amount),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context)!.pleaseEnterAmount;
                    }
                    if (double.tryParse(value) == null) {
                      return AppLocalizations.of(context)!.pleaseEnterValidNumber;
                    }
                    return null;
                  },
                ),
                TextFormField(
                  controller: _reasonController,
                  decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.reason),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: _addDonation,
                  child: Text(AppLocalizations.of(context)!.addDonation),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.donations),
      ),
      body: ValueListenableBuilder(
        valueListenable: HiveHelper.getDonationsBox().listenable(),
        builder: (context, Box<Donation> box, _) {
          final donations = box.values.toList().cast<Donation>();
          if (donations.isEmpty) {
            return Center(child: Text(localizations.noDonationsYet));
          }
          return ListView.builder(
            itemCount: donations.length,
            itemBuilder: (context, index) {
              final donation = donations[index];
              return Card(
                margin:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ListTile(
                  title: Text(donation.donorName),
                  subtitle: Text(donation.reason ?? ''),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text('₹${donation.amount}'),
                      IconButton(
                        icon: const Icon(Icons.download),
                        onPressed: () => _downloadDonation(donation),
                      ),
                      IconButton(
                        icon: const Icon(Icons.share),
                        onPressed: () => _shareDonation(donation),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddDonationDialog,
        child: const Icon(Icons.add),
      ),
    );
  }
}
