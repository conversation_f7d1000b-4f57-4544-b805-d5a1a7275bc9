import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:share_plus/share_plus.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/utils/extensions.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/shared/widgets/custom_button.dart';
import 'package:wargani/shared/widgets/custom_card.dart';
import 'package:wargani/shared/widgets/custom_text_field.dart';
import 'package:wargani/shared/widgets/loading_overlay.dart';
import 'package:wargani/utils/pdf_generator.dart';

/// Ganeshotsav Templates Screen for generating high-resolution templates
class GaneshotsavTemplatesScreen extends StatefulWidget {
  const GaneshotsavTemplatesScreen({super.key});

  @override
  State<GaneshotsavTemplatesScreen> createState() => _GaneshotsavTemplatesScreenState();
}

class _GaneshotsavTemplatesScreenState extends State<GaneshotsavTemplatesScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _mandalNameController = TextEditingController();
  final _locationController = TextEditingController();
  
  bool _isLoading = false;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: AppConstants.mediumAnimation,
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _mandalNameController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Ganeshotsav Templates',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.orange.shade100,
        foregroundColor: Colors.orange.shade800,
        elevation: 0,
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _animationController,
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 0.3),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: _animationController,
                  curve: Curves.easeOutCubic,
                )),
                child: _buildBody(localizations),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBody(AppLocalizations localizations) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            _buildHeaderSection(),
            const SizedBox(height: 24),
            
            // Configuration section
            _buildConfigurationSection(localizations),
            const SizedBox(height: 24),
            
            // Templates section
            _buildTemplatesSection(localizations),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.temple_hindu,
              size: 48,
              color: Colors.orange.shade600,
            ).animate().scale(duration: 600.ms, curve: Curves.elasticOut),
            const SizedBox(height: 16),
            Text(
              '|| श्री गणेशाय नमः ||',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.orange.shade800,
              ),
              textAlign: TextAlign.center,
            ).animate().fadeIn(delay: 200.ms),
            const SizedBox(height: 8),
            Text(
              'High-Resolution Ganeshotsav Templates',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey.shade700,
              ),
              textAlign: TextAlign.center,
            ).animate().fadeIn(delay: 400.ms),
            const SizedBox(height: 8),
            Text(
              'Professional donation receipts and expense reports\nwith traditional festive design',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ).animate().fadeIn(delay: 600.ms),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationSection(AppLocalizations localizations) {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: Colors.orange.shade600,
                ),
                const SizedBox(width: 8),
                Text(
                  'Template Configuration',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange.shade800,
                  ),
                ),
              ],
            ).animate().slideX(delay: 200.ms),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _mandalNameController,
              labelText: 'Mandal Name (मंडळाचे नाव)',
              hintText: 'e.g., श्री गणेश',
              prefixIcon: Icons.account_balance,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Please enter mandal name';
                }
                return null;
              },
            ).animate().slideX(delay: 400.ms),
            
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _locationController,
              labelText: 'Location (स्थान)',
              hintText: 'e.g., पुणे',
              prefixIcon: Icons.location_on,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Please enter location';
                }
                return null;
              },
            ).animate().slideX(delay: 600.ms),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplatesSection(AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.picture_as_pdf,
              color: Colors.orange.shade600,
            ),
            const SizedBox(width: 8),
            Text(
              'Generate Templates',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.orange.shade800,
              ),
            ),
          ],
        ).animate().slideX(delay: 800.ms),
        const SizedBox(height: 16),
        
        // Donation Receipt Template
        _buildTemplateCard(
          title: 'Donation Receipt Template',
          subtitle: 'देणगी पावती टेम्प्लेट',
          description: 'Professional donation receipt with Lord Ganesha image\nand bilingual form fields',
          icon: Icons.receipt_long,
          color: Colors.green,
          onTap: () => _generateDonationTemplate(),
        ).animate().slideY(delay: 1000.ms),
        
        const SizedBox(height: 16),
        
        // Expense Report Template
        _buildTemplateCard(
          title: 'Expense Report Template',
          subtitle: 'खर्च अहवाल टेम्प्लेट',
          description: 'Bank statement style expense report\nwith professional table format',
          icon: Icons.account_balance_wallet,
          color: Colors.blue,
          onTap: () => _generateExpenseTemplate(),
        ).animate().slideY(delay: 1200.ms),
      ],
    );
  }

  Widget _buildTemplateCard({
    required String title,
    required String subtitle,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return CustomCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: color.shade700,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: color.shade800,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: color.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: color.shade400,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _generateDonationTemplate() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final pdfPath = await PdfGenerator.generateGaneshotsavDonationTemplate(
        mandalName: _mandalNameController.text.trim(),
        location: _locationController.text.trim(),
      );

      if (pdfPath != null) {
        await Share.shareXFiles([XFile(pdfPath)]);
        if (mounted) {
          context.showSuccessSnackBar('Donation receipt template generated successfully!');
        }
      } else {
        if (mounted) {
          context.showErrorSnackBar('Failed to generate template');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Error: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _generateExpenseTemplate() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final pdfPath = await PdfGenerator.generateGaneshotsavExpenseTemplate(
        mandalName: _mandalNameController.text.trim(),
        location: _locationController.text.trim(),
      );

      if (pdfPath != null) {
        await Share.shareXFiles([XFile(pdfPath)]);
        if (mounted) {
          context.showSuccessSnackBar('Expense report template generated successfully!');
        }
      } else {
        if (mounted) {
          context.showErrorSnackBar('Failed to generate template');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('Error: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
