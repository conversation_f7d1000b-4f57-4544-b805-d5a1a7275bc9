# Ganeshotsav Mandal Templates

## Overview

This document describes the high-resolution dual-template layout designed for Ganeshotsav Mandals, featuring professional donation receipts and expense reports with traditional festive design elements.

## Templates Included

### 1. Donation Receipt Template
**File**: `generateGaneshotsavDonationTemplate()`

#### Features:
- **Layout**: Dual-column design with <PERSON> image placeholder on the left
- **Form Fields**: Bilingual (Marathi-English-Hindi) editable form on the right
- **Resolution**: 300 DPI print quality
- **Colors**: Traditional saffron, red, gold, and white color scheme
- **Decorations**: Floral borders and ethnic decorations

#### Form Fields:
```
|| श्री गणेशाय नमः ||

_____ समाजसेवा मंडळ, _____

पावती नं. / Receipt No.: ___________
दिनांक / Date: ___________
सौ./श्री / Mr./Mrs.: ___________
यांच्याकडून / Received from: ___________
अक्षरी रुपये / Amount in Words: ___________
रक्कम / Amount ₹: ___________
सही / Signature: ___________

गणपती बाप्पा मोरया! / Ganpati Bappa Morya!
```

### 2. Expense Report Template
**File**: `generateGaneshotsavExpenseTemplate()`

#### Features:
- **Layout**: Professional bank statement format
- **Table Structure**: Clean tabular design with proper headers
- **Resolution**: 300 DPI print quality
- **Colors**: Matching traditional color scheme
- **Format**: Export-friendly layout

#### Table Structure:
```
|| श्री गणेशाय नमः ||

_____ समाजसेवा मंडळ, _____

खर्च अहवाल / Expense Report

| दिनांक / Date | विवरण / Description | रक्कम / Amount ₹ | शिल्लक / Balance ₹ |
|---------------|---------------------|-------------------|---------------------|
| ___________   | ___________         | ___________       | ___________         |
| ___________   | ___________         | ___________       | ___________         |
| ...           | ...                 | ...               | ...                 |

                                      एकूण / Total: ___________

गणपती बाप्पा मोरया! / Ganpati Bappa Morya!
```

## Design Specifications

### Color Palette
- **Saffron**: `#FF9933` - Primary header background
- **Red**: `#DC143C` - Text highlights and accents
- **Gold**: `#FFD700` - Gradient effects and borders
- **Dark Red**: `#8B0000` - Main text and borders
- **Light Saffron**: `#FFF8DC` - Background highlights

### Typography
- **Primary Font**: Noto Sans Devanagari (for proper conjunct character support)
- **Fallback Fonts**: Noto Serif Devanagari, Hind
- **Sizes**: 
  - Header: 18pt (bold)
  - Mandal Name: 16pt (bold)
  - Section Titles: 14pt (bold)
  - Form Fields: 12pt
  - Table Content: 10-12pt

### Layout Specifications
- **Page Format**: A4 (210 × 297 mm)
- **Margins**: 20mm all sides
- **Border Radius**: 15px for main container, 12px for sections
- **Border Width**: 3px for main border, 1-2px for internal elements

## Technical Implementation

### PDF Generation
```dart
// Generate donation template
final donationPath = await PdfGenerator.generateGaneshotsavDonationTemplate(
  mandalName: 'श्री गणेश',
  location: 'पुणे',
);

// Generate expense template
final expensePath = await PdfGenerator.generateGaneshotsavExpenseTemplate(
  mandalName: 'श्री गणेश',
  location: 'पुणे',
);
```

### Font Handling
- **Unicode Support**: Proper handling of Devanagari conjunct characters
- **Text Processing**: Advanced Unicode fixes for श्री, प्र, क्ष, etc.
- **Fallback System**: Multiple font attempts for maximum compatibility

### Export Features
- **High Resolution**: 300 DPI for professional printing
- **Share Integration**: Direct sharing via WhatsApp, email, etc.
- **File Format**: PDF with embedded fonts
- **Compatibility**: Works on both mobile and web platforms

## Usage Instructions

### 1. Access Templates
- Open the Wargani app
- Navigate to the "Templates" tab in the bottom navigation
- Enter your Mandal name and location

### 2. Generate Templates
- Select "Donation Receipt Template" for donation receipts
- Select "Expense Report Template" for expense tracking
- Templates will be generated with your Mandal details

### 3. Customize and Print
- Generated PDFs have blank fields with underlines
- Fill in the fields manually or digitally
- Print at 300 DPI for best quality
- Use high-quality paper for professional appearance

## Features

### Multilingual Support
- **Marathi**: Primary language with proper Devanagari rendering
- **English**: Secondary language for broader accessibility
- **Hindi**: Additional support for Hindi-speaking users

### Professional Design
- **Traditional Elements**: Incorporates Hindu religious symbols
- **Modern Layout**: Clean, organized structure
- **Print-Ready**: Optimized for physical printing
- **Digital-Friendly**: Works well on screens and digital devices

### Customization
- **Editable Fields**: All dynamic content can be customized
- **Mandal Branding**: Include your organization's name and location
- **Flexible Usage**: Suitable for various types of mandals and organizations

## File Structure

```
lib/
├── utils/
│   └── pdf_generator.dart              # Main PDF generation logic
├── screens/
│   └── ganeshotsav_templates_screen.dart  # UI for template generation
└── test_ganeshotsav_templates.dart     # Testing utilities
```

## Testing

Use the test file to verify template generation:

```dart
// Test both templates
await TestGaneshotsavTemplates.testBothTemplates();

// Generate custom templates
await TestGaneshotsavTemplates.generateCustomTemplates(
  mandalName: 'Your Mandal Name',
  location: 'Your Location',
);
```

## Requirements

### Dependencies
- `pdf: ^3.11.3` - PDF generation
- `printing: ^5.13.1` - PDF printing and sharing
- `flutter/services.dart` - Font loading
- `share_plus: ^8.0.0` - File sharing

### Assets
- `assets/fonts/Hind-Regular.ttf` - Fallback Devanagari font
- Internet connection for Google Fonts (primary fonts)

## Best Practices

1. **Font Loading**: Always test font rendering on target devices
2. **Unicode Handling**: Verify proper display of conjunct characters
3. **Print Testing**: Test print output on actual printers
4. **File Management**: Organize generated PDFs in appropriate directories
5. **User Experience**: Provide clear feedback during PDF generation

## Troubleshooting

### Common Issues
1. **Font Rendering**: If Devanagari text appears broken, ensure proper font loading
2. **PDF Generation**: Check internet connectivity for Google Fonts
3. **File Sharing**: Verify app permissions for file access and sharing
4. **Print Quality**: Use 300 DPI settings for best print results

### Solutions
- Implement fallback fonts for offline usage
- Add error handling for font loading failures
- Provide user feedback during long operations
- Test on multiple devices and screen sizes

---

**Note**: These templates are designed specifically for Ganeshotsav Mandals and Hindu religious organizations. The design incorporates traditional elements while maintaining professional standards suitable for official documentation and record-keeping.
