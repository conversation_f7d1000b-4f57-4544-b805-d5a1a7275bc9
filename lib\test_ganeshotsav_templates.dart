import 'package:flutter/material.dart';
import 'package:wargani/utils/pdf_generator.dart';
import 'package:share_plus/share_plus.dart';

/// Test file to demonstrate Ganeshotsav template generation
class TestGaneshotsavTemplates {
  
  /// Test donation template generation
  static Future<void> testDonationTemplate() async {
    try {
      print('🧾 Testing Ganeshotsav Donation Template Generation...');
      
      final pdfPath = await PdfGenerator.generateGaneshotsavDonationTemplate(
        mandalName: 'श्री गणेश',
        location: 'पुणे',
      );
      
      if (pdfPath != null) {
        print('✅ Donation template generated successfully: $pdfPath');
        // Share the PDF
        await Share.shareXFiles([XFile(pdfPath)]);
      } else {
        print('❌ Failed to generate donation template');
      }
    } catch (e) {
      print('❌ Error generating donation template: $e');
    }
  }
  
  /// Test expense template generation
  static Future<void> testExpenseTemplate() async {
    try {
      print('📊 Testing Ganeshotsav Expense Template Generation...');
      
      final pdfPath = await PdfGenerator.generateGaneshotsavExpenseTemplate(
        mandalName: 'श्री गणेश',
        location: 'पुणे',
      );
      
      if (pdfPath != null) {
        print('✅ Expense template generated successfully: $pdfPath');
        // Share the PDF
        await Share.shareXFiles([XFile(pdfPath)]);
      } else {
        print('❌ Failed to generate expense template');
      }
    } catch (e) {
      print('❌ Error generating expense template: $e');
    }
  }
  
  /// Test both templates
  static Future<void> testBothTemplates() async {
    print('🎉 Testing Ganeshotsav Templates...');
    print('=================================');
    
    await testDonationTemplate();
    print('');
    await testExpenseTemplate();
    
    print('=================================');
    print('🎉 Template testing completed!');
  }
  
  /// Generate templates with custom mandal details
  static Future<void> generateCustomTemplates({
    required String mandalName,
    required String location,
  }) async {
    try {
      print('🏛️ Generating custom templates for: $mandalName, $location');
      
      // Generate donation template
      final donationPath = await PdfGenerator.generateGaneshotsavDonationTemplate(
        mandalName: mandalName,
        location: location,
      );
      
      // Generate expense template
      final expensePath = await PdfGenerator.generateGaneshotsavExpenseTemplate(
        mandalName: mandalName,
        location: location,
      );
      
      if (donationPath != null && expensePath != null) {
        print('✅ Both templates generated successfully!');
        print('📄 Donation template: $donationPath');
        print('📊 Expense template: $expensePath');
        
        // Share both files
        await Share.shareXFiles([
          XFile(donationPath),
          XFile(expensePath),
        ]);
      } else {
        print('❌ Failed to generate one or both templates');
      }
    } catch (e) {
      print('❌ Error generating custom templates: $e');
    }
  }
}

/// Widget to test templates in the app
class GaneshotsavTemplateTestWidget extends StatefulWidget {
  const GaneshotsavTemplateTestWidget({super.key});

  @override
  State<GaneshotsavTemplateTestWidget> createState() => _GaneshotsavTemplateTestWidgetState();
}

class _GaneshotsavTemplateTestWidgetState extends State<GaneshotsavTemplateTestWidget> {
  final _mandalController = TextEditingController(text: 'श्री गणेश');
  final _locationController = TextEditingController(text: 'पुणे');
  bool _isLoading = false;

  @override
  void dispose() {
    _mandalController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Ganeshotsav Templates'),
        backgroundColor: Colors.orange.shade100,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Text(
                      '|| श्री गणेशाय नमः ||',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _mandalController,
                      decoration: const InputDecoration(
                        labelText: 'Mandal Name',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _locationController,
                      decoration: const InputDecoration(
                        labelText: 'Location',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              Column(
                children: [
                  ElevatedButton.icon(
                    onPressed: _generateDonationTemplate,
                    icon: const Icon(Icons.receipt_long),
                    label: const Text('Generate Donation Template'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.all(16),
                    ),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton.icon(
                    onPressed: _generateExpenseTemplate,
                    icon: const Icon(Icons.account_balance_wallet),
                    label: const Text('Generate Expense Template'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.all(16),
                    ),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton.icon(
                    onPressed: _generateBothTemplates,
                    icon: const Icon(Icons.picture_as_pdf),
                    label: const Text('Generate Both Templates'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.all(16),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _generateDonationTemplate() async {
    setState(() => _isLoading = true);
    try {
      await TestGaneshotsavTemplates.testDonationTemplate();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Donation template generated!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _generateExpenseTemplate() async {
    setState(() => _isLoading = true);
    try {
      await TestGaneshotsavTemplates.testExpenseTemplate();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Expense template generated!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _generateBothTemplates() async {
    setState(() => _isLoading = true);
    try {
      await TestGaneshotsavTemplates.generateCustomTemplates(
        mandalName: _mandalController.text.trim(),
        location: _locationController.text.trim(),
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Both templates generated!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }
}
