import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:wargani/core/constants/app_constants.dart';
import 'package:wargani/core/theme/app_theme.dart';
import 'package:wargani/l10n/app_localizations.dart';
import 'package:wargani/screens/login_screen.dart';
import 'package:wargani/screens/home_screen.dart';
import 'package:wargani/features/dashboard/presentation/screens/enhanced_dashboard_screen.dart';
import 'package:wargani/features/receipts/presentation/screens/enhanced_wargani_receipt_screen.dart';
import 'package:wargani/features/donations/presentation/screens/enhanced_donations_screen.dart';
import 'package:wargani/features/expenses/presentation/screens/enhanced_expenses_screen.dart';
import 'package:wargani/screens/profile_screen.dart';
import 'package:wargani/screens/ganeshotsav_templates_screen.dart';
import 'package:wargani/utils/hive_helper.dart';

/// Main application widget with professional architecture
class WarganiApp extends ConsumerStatefulWidget {
  const WarganiApp({super.key});

  @override
  ConsumerState<WarganiApp> createState() => _WarganiAppState();
}

class _WarganiAppState extends ConsumerState<WarganiApp> {
  Locale? _locale;

  @override
  void initState() {
    super.initState();
    _loadLocale();
  }

  void _loadLocale() {
    final savedLocale = HiveHelper.getLocale();
    if (savedLocale != null) {
      setState(() {
        _locale = Locale(savedLocale);
      });
    }
  }

  void setLocale(Locale locale) {
    setState(() {
      _locale = locale;
    });
    HiveHelper.saveLocale(locale.languageCode);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      
      // Theme Configuration
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.light, // Can be made dynamic later
      
      // Localization Configuration
      locale: _locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en', ''),
        Locale('mr', ''),
        Locale('hi', ''),
      ],
      
      // Navigation Configuration
      home: const LoginScreen(),
      routes: {
        '/home': (context) => const HomeScreen(),
        '/dashboard': (context) => const HomeScreen(),
        '/wargani': (context) => const HomeScreen(),
        '/donations': (context) => const HomeScreen(),
        '/expenses': (context) => const HomeScreen(),
        '/templates': (context) => const GaneshotsavTemplatesScreen(),
        '/profile': (context) => const HomeScreen(),
      },
      
      // Global Material App Configuration
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: const TextScaler.linear(1.0), // Prevent system font scaling
          ),
          child: child!,
        );
      },
    );
  }
}
